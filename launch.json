{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "bscli - data_qa - plot - IV - PROD",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "prod",
        "AWS_PROFILE": "prod",
        "TARGET_CONFIG": "iv",
        "OUTPUT_SEPARATOR": "tenors"
      }
    },
    {
      "name": "bscli - data_qa - dump - IV - PROD",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "prod",
        "AWS_PROFILE": "prod",
        "TARGET_CONFIG": "iv",
        "OUTPUT_TARGET": "dump",
        "DUMP_PATH": "${workspaceFolder}/bscli/data_qa/data_dumps"
        // "OUTPUT_SEPARATOR": "tenors"
      }
    },
    {
      "name": "bscli - data_qa - plot - ANALYTICS - px_option - PROD",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "prod",
        "AWS_PROFILE": "prod",
        "TARGET_CONFIG": "analytics_px_option",
        "OUTPUT_TARGET": "plot",
        "OUTPUT_SEPARATOR": "tenors"
      }
    },
    {
      "name": "bscli - data_qa - plot - PX - option - PROD",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "prod",
        "AWS_PROFILE": "prod",
        "TARGET_CONFIG": "px_option",
        // "TARGET_CONFIG": "px",
        "OUTPUT_TARGET": "plot",
        "OUTPUT_SEPARATOR": "tenors"
      }
    },
    {
      "name": "bscli - data_qa - plot - PX - perp - PROD",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "prod",
        "AWS_PROFILE": "prod",
        "TARGET_CONFIG": "px_perp",
        "OUTPUT_TARGET": "plot"
      }
    },
    {
      "name": "bscli - data_qa - plot - PX - spot - PROD",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "prod",
        "AWS_PROFILE": "prod",
        "TARGET_CONFIG": "px_spot",
        "OUTPUT_TARGET": "plot"
      }
    },
    {
      "name": "bscli - data_qa - plot - PX - future - PROD",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "prod",
        "AWS_PROFILE": "prod",
        "TARGET_CONFIG": "px_future",
        "OUTPUT_TARGET": "plot",
        "OUTPUT_SEPARATOR": "tenors"
      }
    },
    {
      "name": "bscli - data_qa - dump - PX - option - PROD",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "prod",
        "AWS_PROFILE": "prod",
        "TARGET_CONFIG": "px_option",
        "OUTPUT_TARGET": "dump",
        "DUMP_PATH": "${workspaceFolder}/bscli/data_qa/data_dumps"
      }
    },
    {
      "name": "bscli - data_qa - dump_raw - PX - option - PROD",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "prod",
        "AWS_PROFILE": "prod",
        "TARGET_CONFIG": "px_option",
        "OUTPUT_TARGET": "dump",
        "DUMP_PATH": "${workspaceFolder}/bscli/data_qa/data_dumps",
        "DUMP_RAW": "1"
      }
    },
    {
      "name": "bscli - data_qa - dump - PX - perp - PROD",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "prod",
        "AWS_PROFILE": "prod",
        "TARGET_CONFIG": "px_perp",
        "OUTPUT_TARGET": "dump",
        "DUMP_PATH": "${workspaceFolder}/bscli/data_qa/data_dumps"
      }
    },
    {
      "name": "bscli - data_qa - dump - PX - spot - PROD",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "prod",
        "AWS_PROFILE": "prod",
        "TARGET_CONFIG": "px_spot",
        "OUTPUT_TARGET": "dump",
        "DUMP_PATH": "${workspaceFolder}/bscli/data_qa/data_dumps"
      }
    },
    {
      "name": "bscli - data_qa - dump - PX - future - PROD",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "prod",
        "AWS_PROFILE": "prod",
        "TARGET_CONFIG": "px_option",
        "OUTPUT_TARGET": "dump",
        "DUMP_PATH": "${workspaceFolder}/bscli/data_qa/data_dumps"
      }
    },
    {
      "name": "bscli - data_qa - plot - IV - STAGING",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "staging",
        "AWS_PROFILE": "staging",
        "TARGET_CONFIG": "iv",
        "OUTPUT_SEPARATOR": "tenors",
        "SSM_INDEX_CONFIG_PATH": "/config/price_indices_generated"
      }
    },
    {
      "name": "bscli - data_qa - dump - IV - STAGING",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "staging",
        "AWS_PROFILE": "staging",
        "TARGET_CONFIG": "iv",
        "OUTPUT_TARGET": "dump",
        "DUMP_PATH": "${workspaceFolder}/bscli/data_qa/data_dumps",
        "SSM_INDEX_CONFIG_PATH": "/config/price_indices_generated"
        // "OUTPUT_SEPARATOR": "tenors"
      }
    },
    {
      "name": "bscli - data_qa - plot - PX - option - STAGING",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "staging",
        "AWS_PROFILE": "staging",
        "TARGET_CONFIG": "px_option",
        "OUTPUT_TARGET": "plot",
        "OUTPUT_SEPARATOR": "tenors",
        "SSM_INDEX_CONFIG_PATH": "/config/price_indices_generated"
      }
    },
    {
      "name": "bscli - data_qa - dump_raw - PX - option - STAGING",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "staging",
        "AWS_PROFILE": "staging",
        "TARGET_CONFIG": "px_option",
        "OUTPUT_TARGET": "dump",
        "DUMP_PATH": "${workspaceFolder}/bscli/data_qa/data_dumps",
        "DUMP_RAW": "1",
        "SSM_INDEX_CONFIG_PATH": "/config/price_indices_generated"
      }
    },
    {
      "name": "bscli - data_qa - plot - PX - spot - STAGING",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "staging",
        "AWS_PROFILE": "staging",
        "TARGET_CONFIG": "px_spot",
        "OUTPUT_TARGET": "plot",
        "SSM_INDEX_CONFIG_PATH": "/config/price_indices_generated"
      }
    },
    {
      "name": "bscli - data_qa - plot - PX - future - STAGING",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "staging",
        "AWS_PROFILE": "staging",
        "TARGET_CONFIG": "px_future",
        "OUTPUT_TARGET": "plot",
        "OUTPUT_SEPARATOR": "tenors",
        "SSM_INDEX_CONFIG_PATH": "/config/price_indices_generated"
      }
    },
    {
      "name": "bscli - data_qa - dump - PX - option - STAGING",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "staging",
        "AWS_PROFILE": "staging",
        "TARGET_CONFIG": "px_option",
        "OUTPUT_TARGET": "dump",
        "DUMP_PATH": "${workspaceFolder}/bscli/data_qa/data_dumps",
        "SSM_INDEX_CONFIG_PATH": "/config/price_indices_generated"
      }
    },
    {
      "name": "bscli - data_qa - plot - PX - perp - STAGING",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "staging",
        "AWS_PROFILE": "staging",
        "TARGET_CONFIG": "px_perp",
        "OUTPUT_TARGET": "plot",
        "SSM_INDEX_CONFIG_PATH": "/config/price_indices_generated"
      }
    },
    {
      "name": "bscli - data_qa - dump - PX - spot - STAGING",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "staging",
        "AWS_PROFILE": "staging",
        "TARGET_CONFIG": "px_spot",
        "OUTPUT_TARGET": "dump",
        "DUMP_PATH": "${workspaceFolder}/bscli/data_qa/data_dumps",
        "SSM_INDEX_CONFIG_PATH": "/config/price_indices_generated"
      }
    },
    {
      "name": "bscli - data_qa - dump_raw - PX - spot - STAGING",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "staging",
        "AWS_PROFILE": "staging",
        "TARGET_CONFIG": "px_spot",
        "OUTPUT_TARGET": "dump",
        "DUMP_PATH": "${workspaceFolder}/bscli/data_qa/data_dumps",
        "DUMP_RAW": "1",
        "SSM_INDEX_CONFIG_PATH": "/config/price_indices_generated"
      }
    },
    {
      "name": "bscli - data_qa - dump - PX - future - STAGING",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["data_qa"],
      "env": {
        "AWS_ENV": "staging",
        "AWS_PROFILE": "staging",
        "TARGET_CONFIG": "px_option",
        "OUTPUT_TARGET": "dump",
        "DUMP_PATH": "${workspaceFolder}/bscli/data_qa/data_dumps",
        "SSM_INDEX_CONFIG_PATH": "/config/price_indices_generated"
      }
    },
    {
      "name": "bscli - fetch_client_subs - PROD",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["fetch_client_subs"],
      "env": {
        "AWS_ENV": "prod",
        "AWS_PROFILE": "prod",
        "QUERY_START": "2025-01-13T00:00:00Z",
        "QUERY_END": "2025-01-14T00:00:00Z",
        "API_KEY_NAME_SEARCH_STRING": "Jasper",
        "SUB_SEARCH_STRING_INCLUDE": "moneyness.iv",
        "SUB_SEARCH_STRING_EXCLUDE": "BS_ENG_TEST_JASPER"
      }
    },
    {
      "name": "bscli - fetch_client_subs - STAGING",
      "type": "debugpy",
      "request": "launch",
      "program": "${workspaceFolder}/bscli/bscli.py",
      "console": "integratedTerminal",
      "justMyCode": false,
      "args": ["fetch_client_subs"],
      "env": {
        "AWS_ENV": "staging",
        "AWS_PROFILE": "staging",
        "QUERY_START": "2025-01-10T21:00:00Z",
        "QUERY_END": "2025-01-10T22:00:00Z",
        "API_KEY_NAME_SEARCH_STRING": "Derive",
        "SSM_INDEX_CONFIG_PATH": "/config/price_indices_generated"
        // "SUB_SEARCH_STRING_INCLUDE": "verifying_contract",
        // "SUB_SEARCH_STRING_EXCLUDE": "957",
      }
    }
  ]
}
