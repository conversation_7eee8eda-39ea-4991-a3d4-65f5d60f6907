#!/usr/bin/env python3.8

import argparse
import asyncio
import logging
import math
import os
import re
import time
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from functools import partial
from typing import (
    Any,
    Callable,
    DefaultDict,
    Dict,
    Generator,
    List,
    Literal,
    Optional,
    Set,
    Tuple,
    TypedDict,
    TypeVar,
    Union,
    cast,
)

import backoff
import github
import utils_general
from github import Github
from github.Branch import Branch
from github.Commit import Commit
from github.GithubException import GithubException
from github.PaginatedList import PaginatedList
from github.Repository import Repository
from github.Workflow import Workflow
from github.WorkflowRun import WorkflowRun

# parse program args
parser = argparse.ArgumentParser(formatter_class=argparse.ArgumentDefaultsHelpFormatter)
parser.add_argument(
    "-repo",
    type=str.lower,
    default="",
    help="Repo(s) to prepare for deployment. Accepts single or comma-separated list. If unspecified, all relevant repos will be prepped.",
)
parser.add_argument(
    "-log_level",
    type=str.upper,
    default="BSDEBUG",
    help="Logging severity level. Can also be set as env var (`LOG_LEVEL`)",
)
parser.add_argument(
    "-github_token",
    type=str,
    default="",
    help="Github token used for authenticated API calls. Can also be set as env var (`GITHUB_TOKEN`)",
)
args = parser.parse_args()


HandleRepoSteps = Literal[
    "no_successful_prod_promotion_found",
    "prod_branch_reset_to_date",
    "new_prod_branch_created",
    "release_pr_created",
]
DeployTypes = Literal["stream", "function", "other"]


class StepsResult(TypedDict):
    no_successful_prod_promotion_found: bool
    prod_branch_reset_to_date: str
    new_prod_branch_created: bool
    release_pr_created: bool


class AggregatedRepoStepsAttributes(TypedDict):
    no_successful_prod_promotion_found: DefaultDict[DeployTypes, Set[str]]  # repo
    prod_branch_reset_to_date: DefaultDict[
        DeployTypes, Set[Tuple[str, str]]
    ]  # (repo, date)
    new_prod_branch_created: DefaultDict[DeployTypes, Set[str]]  # repo
    release_pr_created: DefaultDict[DeployTypes, Set[str]]  # repo


class HandleRepoResult(TypedDict):
    name: str
    steps: DefaultDict[DeployTypes, StepsResult]
    error: Optional[str]


RequestType = Literal["GET", "POST", "PUT", "PATCH", "DELETE"]


class RateLimitArgs(TypedDict):
    lock: asyncio.Lock
    reset_time: Optional[datetime]
    wait_time: Optional[float]
    backoff: Generator[int, Any, Any]
    retries: int


class BranchStub:
    def __init__(self, name: str):
        self.name = name


utils_general.setup_python_logger(args.log_level or os.getenv("LOG_LEVEL", "BSDEBUG"))
G = Github(
    auth=github.Auth.Token(args.github_token or os.getenv("GITHUB_TOKEN", "")),
    pool_size=1000,
)
EXECUTOR = ThreadPoolExecutor(max_workers=100)
NO_SUCCESSFUL_PROD_PROMOTION_FOUND = "no_successful_prod_promotion_found"
PROD_BRANCH_RESET_TO_DATE = "prod_branch_reset_to_date"
NEW_PROD_BRANCH_CREATED = "new_prod_branch_created"
RELEASE_PR_CREATED = "release_pr_created"
PROD_BRANCH_NAMES = {
    "stream": "production_stream",
    "function": "production_function",
    "other": "production",
}
EXCLUDED_REPOS = {
    "unhandled_deploy": set(
        [
            "cloudformation-templates",
            "data-analysis-UI",
            "miscLambdas",
        ]
    ),
    "no_deploy": set(
        [
            "api-docs",
            "aws_sqs_wrapper",
            "Barrier-Options",
            "BlockScholes-api-docs",
            "docker-base-apps",
            "bscli",
            "bslang",
            "calcvolsurface",
            "data-analyzer-script",
            "dataCrawler",
            "DataProviderManager-Catalog",
            "Defi-addresses",
            "defiDataGrabber-1",
            "DeribitNodejs",
            "docs",
            "fundingRate",
            "futuresCurve",
            "generateAndPersistMarketData",
            "generateMonthlyContract",
            "getFutureMidPrice",
            "getFuturesCurve",
            "getFuturesHeatmap",
            "getModelParameters",
            "getOptionsHeatMap",
            "getTimeseriesByKey",
            "getVolSurface",
            "data-computation-service-test",
            "HistUploader",
            "Ideatolife",
            "impliedVolCalc",
            "JSONCatalogSchema",
            "JSONDataSchema",
            "JuneLibrary",
            "libSABR-Layer",
            "modelDataUploader",
            "optionPricerWithCalib-1",
            "pythonliblayersabr",
            "Range-Accrual",
            "RTServer",
            "SABR_CALIB_GIT",
            "StochasticLocalVol",
            "StormCalculations",
            "superCompositeCalc",
            "SVI_SABR_CONVERSIONS_EC2",
            "SVIFileGeneratorLambda",
            "synchSQSMessageSender",
            "TardisDataDownloader",
            "technical-indicators",
            "tickDataMigration",
            "timeseriesCatalog",
            "valueAggregator",
            "Vol-Surface-SVI-",
            "volSurfaceAPI",
            "volSurfaceCalibration",
            "workflows",
        ]
    ),
    "deprecated": set(
        [
            "blockscholesAPIAuthSam",
            "libSABRSVI-Layer",
            "futuresPriceIndex",
            "catalogs-lambda",
            "gapfiller",
        ]
    ),
}
ALL_EXCLUDED_REPOS = cast(
    Set[str],
    set(
        repo_name.lower()  # type:ignore can't narrow
        for repo_name in set().union(  # type:ignore can't narrow
            *EXCLUDED_REPOS.values()
        )
    ),
)
LOG_SECTION_SEP_CHARNUM = 78
STEP_LOG_MESSAGE = cast(
    Dict[HandleRepoSteps, str],
    {
        NO_SUCCESSFUL_PROD_PROMOTION_FOUND: "NO SUCCESSFUL PROD PROMOTION RUNS FOUND",
        PROD_BRANCH_RESET_TO_DATE: "PRODUCTION BRANCH RESET",
        NEW_PROD_BRANCH_CREATED: "CREATED NEW PROD BRANCH",
        RELEASE_PR_CREATED: "RELEASE -> PROD PR CREATED",
    },
)
T = TypeVar("T")
BSDEBUG = 15
LOOP = asyncio.get_event_loop()
MUTATING_CALL_RATE_LIMIT_OBJ: RateLimitArgs = {
    "lock": asyncio.Lock(),
    "reset_time": None,
    "wait_time": None,
    "backoff": backoff.expo(factor=3, base=2, max_value=90),
    "retries": 15,
}
GET_RATE_LIMIT_OBJ: RateLimitArgs = {
    "lock": asyncio.Lock(),
    "reset_time": None,
    "wait_time": None,
    "backoff": backoff.expo(factor=2, base=2, max_value=60),
    "retries": 10,
}

RATE_LIMIT_STATE: Dict[RequestType, RateLimitArgs] = {
    "GET": GET_RATE_LIMIT_OBJ,
    "PUT": MUTATING_CALL_RATE_LIMIT_OBJ,
    "POST": MUTATING_CALL_RATE_LIMIT_OBJ,
    "PATCH": MUTATING_CALL_RATE_LIMIT_OBJ,
    "DELETE": MUTATING_CALL_RATE_LIMIT_OBJ,
}


async def _call_async(func: Callable[..., T], *args: Any, **kwargs: Any) -> T:
    partial_func = partial(func, *args, **kwargs)
    return await LOOP.run_in_executor(EXECUTOR, partial_func)


def _should_giveup(e: Exception) -> bool:
    if isinstance(e, GithubException) and hasattr(e, "headers") and e.status == 403:
        headers = {k.lower(): v for k, v in e.headers.items()}  # type:ignore
        return (
            not (
                "retry-after" in headers
                or "x-ratelimit-remaining" in headers
                or "x-ratelimit-reset" in headers
            )
            or "Repository was archived so is read-only" in e.data["message"]
        )
    elif isinstance(e, GithubException) and e.status == 403:
        return "You have exceeded a secondary rate limit" not in e.data["message"]
    return True


def _reset_backoff(req_type: RequestType):
    rlimit_s = RATE_LIMIT_STATE[req_type]
    rlimit_s["reset_time"] = None
    rlimit_s["wait_time"] = None
    rlimit_s["backoff"] = backoff.expo(
        factor=2 if req_type == "GET" else 3,
        base=2,
        max_value=30 if req_type == "GET" else 60,
    )
    # account for initial no-op call of backoff.expo
    next(rlimit_s["backoff"])


def _wait_gen(
    retry_after: Optional[str],
    req_type: RequestType,
    rate_limit_remaining: Optional[str],
    rate_limit_reset: Optional[str],
):
    rlimit_s = RATE_LIMIT_STATE[req_type]

    # Handle the Retry-After header
    if retry_after != None:
        rlimit_s["wait_time"] = int(retry_after)
        logging.log(BSDEBUG, f"retry_after WAIT: {rlimit_s['wait_time']}s")
        yield rlimit_s["wait_time"]
        return

    # Handle rate_limit_remaining and reset_time headers
    if (
        rate_limit_remaining != None
        and int(rate_limit_remaining) == 0
        and rate_limit_reset != None
    ):
        rlimit_s["wait_time"] = (
            datetime.utcfromtimestamp(int(rate_limit_reset)) - datetime.utcnow()
        ).total_seconds()
        logging.log(BSDEBUG, f"rate_limit_reset WAIT: {rlimit_s['wait_time']}s")
        yield rlimit_s["wait_time"]
        return

    # Otherwise, use the exponential backoff
    while True:
        next_wait = next(rlimit_s["backoff"])
        rlimit_s["wait_time"] = next_wait
        logging.log(BSDEBUG, f"backoff_expo WAIT: {rlimit_s['wait_time']}s")
        yield next_wait


# TODO: this fn & logic related to rate limiting should be moved to utils_general
async def _rate_limited_api_call(
    func: Callable[..., T],
    *args: Any,
    request_type: RequestType,
    func_name: Optional[str] = None,
    **kwargs: Any,
) -> T:
    rlimit_s = RATE_LIMIT_STATE[request_type]

    for _ in range(rlimit_s["retries"]):
        try:
            if rlimit_s["wait_time"] != None:
                logging.log(
                    BSDEBUG,
                    f"Wait {rlimit_s['wait_time']}s before lock acquire for {func_name or func.__name__}",
                )
                await asyncio.sleep(rlimit_s["wait_time"])
                async with rlimit_s["lock"]:
                    logging.log(
                        BSDEBUG,
                        f"Lock acquired. Executing {func_name or func.__name__}",
                    )
                    rsp = await _call_async(func, *args, **kwargs)
                    _reset_backoff(request_type)
                    return rsp
            else:
                logging.log(BSDEBUG, f"No wait. Executing {func_name or func.__name__}")
                rsp = await _call_async(func, *args, **kwargs)
                _reset_backoff(request_type)
                return rsp
        except GithubException as e:
            if not _should_giveup(e) and hasattr(e, "headers"):
                headers = {k.lower(): v for k, v in e.headers.items()}  # type:ignore
                retry_after = headers.get("retry-after", None)
                rate_limit_remaining = headers.get("x-ratelimit-remaining", None)
                rate_limit_reset = headers.get("x-ratelimit-reset", None)

                # Calculate the wait time using wait_gen and sleep
                rlimit_s["wait_time"] = next(
                    _wait_gen(
                        retry_after=retry_after,
                        req_type=request_type,
                        rate_limit_remaining=rate_limit_remaining,
                        rate_limit_reset=rate_limit_reset,
                    )
                )
            else:
                raise e
    raise Exception("Max retries reached.")


async def _set_branch_protection(branch: Branch, allow_force_push: bool = False):
    # Update the branch protection
    await _rate_limited_api_call(
        branch.edit_protection,
        contexts=[],
        require_code_owner_reviews=True,
        required_linear_history=True,
        required_approving_review_count=1,
        users_bypass_pull_request_allowances=[""],  # type:ignore bad lib typing
        allow_force_pushes=allow_force_push,
        request_type="PUT",
    )


async def _create_release_branch(
    repo: Repository,
    main: Branch,
):
    """
    Checkout a new branch from main, named "release/<date>"
    :return: Branch. Object representing the release branch
    """
    release_name = f"release/{datetime.utcnow().strftime('%Y-%m-%d')}"
    branches: PaginatedList[Branch] = await _rate_limited_api_call(
        repo.get_branches,
        request_type="GET",
        func_name="get_all_branches",
    )

    async def _create(release_name: str):
        logging.info(f"Creating {release_name} branch for {repo.name}")
        await _rate_limited_api_call(
            repo.create_git_ref,
            ref=f"refs/heads/{release_name}",
            sha=main.commit.sha,
            request_type="POST",
            func_name="create_release_branch",
        )
        await asyncio.sleep(2)  # account for eventual consistency

    same_day_releases = list(
        filter(lambda b: b.name.startswith(release_name), branches)
    )

    # Create new release if none exists for today or most recent one is stale
    if same_day_releases:
        # Compare against most recent same-day release
        if len(same_day_releases) == 1:
            most_recent_release_name = same_day_releases[0].name
        else:

            def get_release_num(rb: Branch) -> float:
                release_num = rb.name.split("_")[-1] if "_" in rb.name else 0
                try:
                    return int(release_num)
                except ValueError:
                    return -math.inf

            most_recent_release_name = max(same_day_releases, key=get_release_num).name

        comparison = await _rate_limited_api_call(
            repo.compare, most_recent_release_name, main.name, request_type="GET"
        )

        if comparison.merge_base_commit.sha != main.commit.sha:
            most_recent_release_tokens = most_recent_release_name.split("_")
            new_release_suf = (
                (f"_{int(most_recent_release_tokens[-1]) + 1}")
                if len(most_recent_release_tokens) > 1
                else "_1"
            )
            release_name = f"{release_name}{new_release_suf}"
            await _create(release_name)
        else:
            release_name = most_recent_release_name
    else:
        await _create(release_name)

    return await _rate_limited_api_call(
        repo.get_branch, release_name, request_type="GET"
    )


# TODO: if a release PR has become stale or simply gets closed due to a rerun,
# it would be nice to reference them in the newly created PR, in case they had
# any relevant comments (or copy those comments over somehow, if possible).
async def _close_automated_production_prs(repo: Repository):
    logging.info(f"Closing all open prod promotion & release PRs for {repo.name}")
    for pr in await _rate_limited_api_call(
        repo.get_pulls, state="open", request_type="GET"
    ):
        if re.match(
            r"\[Automated\].*(Promote staging branch to production|.*release.*->.*production.*)",
            pr.title,
            re.IGNORECASE,
        ):
            await _rate_limited_api_call(
                pr.edit,
                state="closed",
                request_type="PATCH",
                func_name="close_prod_pr",
            )


async def _get_last_relevant_prod_deploys(repo: Repository, main_branch: Branch):
    """
    :return: Dict[DeployTypes, Optional[WorkflowRun]]. Will only contain values
    for existing deploy types. The value is None if it had no successful run
    """
    logging.info(f"Fetching last relevant prod deploys for {repo.name}")
    prod_workflows: DefaultDict[DeployTypes, List[Workflow]] = defaultdict(list)

    for w in await _rate_limited_api_call(repo.get_workflows, request_type="GET"):
        if re.match(r".*prod.*function.*deploy.*", w.name, re.IGNORECASE):
            prod_workflows["function"].append(w)
        elif re.match(r".*prod.*stream.*deploy.*", w.name, re.IGNORECASE):
            prod_workflows["stream"].append(w)
        elif re.match(r".*prod.*deploy.*|Production Release", w.name, re.IGNORECASE):
            prod_workflows["other"].append(w)

    if (
        not prod_workflows["function"]
        and not prod_workflows["stream"]
        and not prod_workflows["other"]
    ):
        raise Exception("Prod workflow missing")
    else:
        for deploy_type in prod_workflows.copy():
            if not prod_workflows[deploy_type]:
                del prod_workflows[deploy_type]

    main_commits = set(
        c.sha
        for c in await _rate_limited_api_call(
            repo.get_commits, main_branch.name, request_type="GET"
        )
    )

    # Get last successful prod deploys made from main
    last_relevant_prod_runs = cast(
        Dict[DeployTypes, Optional[WorkflowRun]],
        {},
    )
    for deploy_type, workflows in prod_workflows.items():
        last_relevant_prod_runs[deploy_type] = None
        # NOTE: at the moment this is always expected to be a single iteration
        # as only 1-to-1 mappings between deploy type and workflow are supported
        for w in workflows:
            runs = await _rate_limited_api_call(w.get_runs, request_type="GET")
            for run in runs:
                is_successful = run.conclusion == "success"
                is_more_recent = (
                    last_relevant_prod_runs[deploy_type] == None
                    or last_relevant_prod_runs[deploy_type].created_at  # type:ignore
                    < run.created_at
                )
                is_from_prod = run.head_branch == PROD_BRANCH_NAMES[deploy_type]
                is_from_main = run.head_sha in main_commits

                if is_successful and is_more_recent and (is_from_prod or is_from_main):
                    last_relevant_prod_runs[deploy_type] = run
                    break

    return last_relevant_prod_runs


async def _create_release_pr(
    repo: Repository, prod_branch: Branch, release_branch: Branch
):
    logging.info(f"Creating release pr for {repo.name} on {prod_branch.name}")
    # Only create a pull request if there are new commits
    comparison = await _rate_limited_api_call(
        repo.compare, prod_branch.name, release_branch.name, request_type="GET"
    )
    if comparison.total_commits == 0:
        return

    # Check if PR already exists
    pulls = await _rate_limited_api_call(
        repo.get_pulls, state="open", sort="created", base="main", request_type="GET"
    )
    for pull in pulls:
        if pull.head.ref == release_branch.name:
            return

    pr = await _rate_limited_api_call(
        repo.create_pull,
        title=f"[Automated] {release_branch.name} -> {prod_branch.name}",
        body=f"This PR is automatically created to promote latest release branch to {prod_branch.name} branch.",
        head=release_branch.name,
        base=prod_branch.name,
        request_type="POST",
        func_name="create_prod_release_pr",
    )
    await _rate_limited_api_call(
        pr.set_labels,
        "automated",
        request_type="PUT",
        func_name="label_prod_release_pr",
    )


async def _create_prod_branch(repo: Repository, name: str, leaf_commit_sha: str):
    await _rate_limited_api_call(
        repo.create_git_ref,
        ref=f"refs/heads/{name}",
        sha=leaf_commit_sha,
        request_type="POST",
        func_name="create_prod_branch",
    )
    prod_branch = await _rate_limited_api_call(
        repo.get_branch, name, request_type="GET", func_name="get_prod_branch"
    )
    await _set_branch_protection(branch=prod_branch, allow_force_push=False)
    return prod_branch


async def _reset_branch(repo: Repository, branch: Branch, new_commit_sha: str):
    # cannot allow force push for admin/specific user only, so will
    # revert that after production reset
    await _set_branch_protection(branch=branch, allow_force_push=True)
    await _rate_limited_api_call(
        repo.get_git_ref(f"heads/{branch.name}").edit,
        new_commit_sha,
        True,
        request_type="PATCH",
        func_name="reset_branch",
    )
    await _set_branch_protection(branch=branch, allow_force_push=False)


async def _is_commit_detached(repo: Repository, commit_sha: str):
    # Check against all branches
    branches = await _rate_limited_api_call(
        repo.get_branches,
        request_type="GET",
        func_name="get_all_branches",
    )

    async def _check_branch(branch: Branch):
        try:
            comparison = await _rate_limited_api_call(
                repo.compare, branch.name, commit_sha, request_type="GET"
            )
            return comparison.status in {"identical", "behind"}
        except Exception as e:
            logging.error(f"Error checking branch {branch.name}: {e}")
            return True

    results = await asyncio.gather(*(_check_branch(b) for b in branches))
    if any(results):
        return False  # Commit is part of a branch

    # Check against all tags
    tags = await _rate_limited_api_call(repo.get_tags, request_type="GET")
    if any(tag.commit.sha == commit_sha for tag in tags):
        return False  # Commit is part of this tag

    return True  # Commit is not part of any branch or tag


async def _handle_repo(repo: Repository):
    assert repo.name.lower() not in ALL_EXCLUDED_REPOS
    handling_agg_repo_steps_attr_defaults = cast(
        StepsResult,
        {
            NO_SUCCESSFUL_PROD_PROMOTION_FOUND: False,
            PROD_BRANCH_RESET_TO_DATE: "",
            NEW_PROD_BRANCH_CREATED: False,
            RELEASE_PR_CREATED: False,
        },
    )
    result: HandleRepoResult = {
        "name": repo.name,
        "steps": defaultdict(lambda: handling_agg_repo_steps_attr_defaults.copy()),
        "error": None,
    }

    try:
        await _close_automated_production_prs(repo)
        main_branch = await _rate_limited_api_call(
            repo.get_branch, "main", request_type="GET", func_name="get_main"
        )
        prod_runs = await _get_last_relevant_prod_deploys(repo, main_branch)

        last_prod_deploy_commits: Dict[DeployTypes, Commit] = {}

        for deploy_type, prod_run in prod_runs.items():
            if prod_run is None:
                last_prod_deploy_commits[deploy_type] = main_branch.commit
                result["steps"][deploy_type][NO_SUCCESSFUL_PROD_PROMOTION_FOUND] = True
            else:
                # Get the commit of the last successful run
                last_prod_deploy_commits[deploy_type] = await _rate_limited_api_call(
                    repo.get_commit,
                    prod_run.head_sha,
                    request_type="GET",
                    func_name="get_prod_run_commit",
                )

        release_branch = None
        for deploy_type, prod_deploy_commit in last_prod_deploy_commits.items():
            prod_branch = None
            prod_branch_name = PROD_BRANCH_NAMES[deploy_type]
            try:
                # Get the prod branch & reset to new commit (last relevant
                # deploy or main) if stale
                prod_branch = await _rate_limited_api_call(
                    repo.get_branch,
                    prod_branch_name,
                    request_type="GET",
                    func_name="get_prod_branch",
                )

                # Resetting prod branch if:
                # - last deployed from backout/other branch
                # - last deployed from prod branch and the commit is not detached
                # due to it having been reset to match last release branch history
                if (
                    prod_deploy_commit.sha != prod_branch.commit.sha
                    and not await _is_commit_detached(repo, prod_deploy_commit.sha)
                ):
                    await _reset_branch(repo, prod_branch, prod_deploy_commit.sha)
                    result["steps"][deploy_type][PROD_BRANCH_RESET_TO_DATE] = (
                        prod_deploy_commit.last_modified or ""
                    )

                elif prod_commit_release_match := re.search(
                    r"release/\d{4}-\d{2}-\d{2}", prod_branch.commit.commit.message
                ):
                    # If last deployed from a prod branch with the head commit as
                    # a release merge, reset prod to match that release. This is
                    # necessary to ensure its history is identical to the release
                    # branch that it was last updated with, such that a diff
                    # between prod & new release is meaningful. Note that simply
                    # merging release branches by rebasing would not be sufficient
                    # as the UI rebase action diverges the histories of production
                    # vs main (behaving differently to the CLI rebase, which does
                    # allow for fast-forward rebases which don't create new commits).
                    # The commit of the last successful run should be set after
                    # this reset.
                    logging.info("Getting last deployed release branch...")
                    last_deployed_release_branch = await _rate_limited_api_call(
                        repo.get_branch,
                        prod_commit_release_match.group(0),
                        request_type="GET",
                        func_name="get_release_branch",
                    )
                    logging.info(
                        f"Found last deployed release branch. Resetting {prod_branch} to last deployed"
                    )

                    await _reset_branch(
                        repo,
                        prod_branch,
                        last_deployed_release_branch.commit.sha,
                    )
                    # TODO: the last_modified doesn't seem to be reliably present here
                    result["steps"][deploy_type][PROD_BRANCH_RESET_TO_DATE] = (
                        last_deployed_release_branch.commit.last_modified or ""
                    )

            except GithubException as e:
                # create prod branch if it doesn't exist
                if e.status == 404:
                    logging.warning(e.data["message"])
                    prod_branch = await _create_prod_branch(
                        repo,
                        prod_branch_name,
                        prod_deploy_commit.commit.sha,
                    )
                    result["steps"][deploy_type][NEW_PROD_BRANCH_CREATED] = True
                else:
                    raise e

            # Create release & PR if production branch is stale
            comparison = await _rate_limited_api_call(
                repo.compare, prod_branch.name, main_branch.name, request_type="GET"
            )
            if comparison.merge_base_commit.sha != main_branch.commit.sha:
                # check if release_branch created on previous iteration, as
                # it should be reused across deploy types
                if release_branch is None:
                    release_branch = await _create_release_branch(repo, main_branch)

                await _create_release_pr(
                    repo,
                    prod_branch,
                    release_branch,
                )
                result["steps"][deploy_type][RELEASE_PR_CREATED] = True

    except Exception as e:
        result["error"] = str(e)

    return result


def _log_results(results: List[HandleRepoResult]):
    # aggregate results & log with appropriate messages
    agg_repo_steps_attr = cast(
        AggregatedRepoStepsAttributes,
        defaultdict(
            lambda: cast(
                Union[
                    DefaultDict[DeployTypes, Set[str]],  # repo
                    DefaultDict[DeployTypes, Set[Tuple[str, str]]],  # (repo, date)
                ],
                defaultdict(set),
            )
        ),
    )
    errors: DefaultDict[str, Set[str]] = defaultdict(set)

    for hrr in results:
        for deploy_type, steps_result in hrr["steps"].items():
            for step, val in steps_result.items():
                if val:
                    if step == PROD_BRANCH_RESET_TO_DATE:
                        agg_repo_steps_attr[step][deploy_type].add(
                            (hrr["name"], steps_result[step])
                        )
                    else:
                        agg_repo_steps_attr[step][
                            deploy_type
                        ].add(  # type:ignore can't narrow
                            hrr["name"]
                        )

        if hrr["error"]:
            errors[hrr["error"]].add(hrr["name"])

    def log_section(msg: str):
        msg_padding = "-" * (((LOG_SECTION_SEP_CHARNUM - len(msg)) - 2) // 2)
        logging.info("\n\n")
        logging.info("=" * LOG_SECTION_SEP_CHARNUM)
        logging.info(f"{msg_padding} {msg} {msg_padding}")
        logging.info("=" * LOG_SECTION_SEP_CHARNUM)

    for step, message in STEP_LOG_MESSAGE.items():
        log_section(message)
        for dt, repo_vals in agg_repo_steps_attr[step].items():
            logging.info(f"Deploy type: {dt}")
            for v in repo_vals:
                if not v:
                    continue
                if step == PROD_BRANCH_RESET_TO_DATE:
                    assert isinstance(v, tuple)
                    repo, date = v
                    logging.info(f"- https://github.com/blockscholes/{repo} to {date}")
                else:  # assuming it's a string in this case
                    logging.info(f"- https://github.com/blockscholes/{v}")

    log_section("ERRORS")
    for error, repos in errors.items():
        logging.info(f"{error}")
        for r in repos:
            logging.info(f"- https://github.com/blockscholes/{r}")


async def run():
    t = time.time()
    # reset to account for initial no-op call of backoff.expo
    _reset_backoff("GET")
    _reset_backoff("POST")

    org = G.get_organization("blockscholes")
    tasks = [
        _handle_repo(repo)
        for repo in await _call_async(org.get_repos)
        if repo.name.lower() not in ALL_EXCLUDED_REPOS
        if (
            not args.repo
            or repo.name.lower() in set(r.lower() for r in args.repo.split(","))
        )
    ]
    _log_results(await asyncio.gather(*tasks))
    logging.log(BSDEBUG, "\n\n")
    logging.log(BSDEBUG, f"Total runtime: {round(time.time()-t)}s")


if __name__ == "__main__":
    # TODO: add github workflow for this as well (& potential cron schedule)
    LOOP.run_until_complete(run())
