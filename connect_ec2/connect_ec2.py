import argparse
import logging
import os
import subprocess
import sys
from typing import Optional

import boto3
import utils_general
from mypy_boto3_ec2.service_resource import Instance

parser = argparse.ArgumentParser(description="SSH into an EC2 instance by name")
parser.add_argument("--profile", required=True, help="AWS CLI profile name")
parser.add_argument(
    "--instance_name",
    default="backfill-jobs-quant",
    help="Name tag of the EC2 instance",
)
parser.add_argument(
    "-log_level",
    type=str.upper,
    default="BSDEBUG",
    help="Logging severity level. Can also be set as env var (`LOG_LEVEL`)",
)

args = parser.parse_args()
utils_general.setup_python_logger(args.log_level or os.getenv("LOG_LEVEL", "BSDEBUG"))


def get_instance(profile: str, instance_name: str) -> Optional[Instance]:
    session = boto3.Session(profile_name=profile)
    ec2 = session.resource("ec2")  # type: ignore partially unknown
    instances = ec2.instances.filter(
        Filters=[{"Name": "tag:Name", "Values": [instance_name]}]
    )

    for instance in instances:
        return instance

    logging.info(f"No instances with the name '{instance_name}' found.")
    return None


def start_instance_if_stopped(instance: Instance) -> None:
    if instance.state["Name"] == "stopped":
        logging.info(f"Starting instance {instance.id}...")
        instance.start()
        instance.wait_until_running()
        logging.info("Instance is now running.")
        instance.reload()


def stop_instance(instance: Instance) -> None:
    logging.info(f"Stopping instance {instance.id}...")
    instance.stop()
    logging.info(
        "Instance is now stopping. It may take a few minutes for it to fully stop."
    )


def run() -> None:
    instance = get_instance(args.profile, args.instance_name)
    if instance is None:
        logging.info(f"Instance tagged with Name={args.instance_name} not found.")
        sys.exit(1)

    logging.info(
        f"Instance {args.instance_name} found:  ID={instance.id}. State={instance.state['Name']}"
    )
    start_instance_if_stopped(instance)

    public_ip = instance.public_ip_address
    ssh_user = "ec2-user"
    ssh_key_path = f'{os.path.expanduser("~")}/.ssh/prod-quant-Blockscholes-KP.pem'

    if public_ip:
        ssh_command = ["ssh", "-i", ssh_key_path, f"{ssh_user}@{public_ip}"]
        logging.info(
            f"Initiating SSH to {ssh_user}@{public_ip} using key {ssh_key_path}..."
        )
        subprocess.run(ssh_command)

        user_input = input(
            "\nWould you like to stop the EC2 instance (should it be kept running / is anyone else using it atm)? y/n: "
        )
        if user_input.lower() == "y":
            stop_instance(instance)
    else:
        logging.info("Instance does not have a public IP address.")
        sys.exit(1)


if __name__ == "__main__":
    run()
