import glob
import logging
import os.path
import sys
import tempfile
from typing import List, Literal, TypedDict

from git import RemoteReference, Repo

logging.basicConfig(level=logging.INFO, stream=sys.stdout)


class TemplateParams(TypedDict):
    camelcase_name: str
    pascalcase_name: str
    lowercase_name: str
    aws_org_id: str
    pascalcase_stage: str
    stage_folder: str
    lowercase_stage: str
    cluster_name: str
    upper_short_stage: str
    repo_name: str
    private_one_subnet: str
    private_two_subnet: str
    vpc_id: str
    template_location: str


changeset_template = """
  get-change-set-{{lowercase_name}}:
    runs-on: arm-runner
    needs: get-changed-files
    if: needs.get-changed-files.outputs.{{lowercase_name}} == 'true'
    env:
      templateFile: file://{{stage_folder}}/{{pascalcase_name}}Stack.yml
      parametersFile: file://{{stage_folder}}/{{pascalcase_name}}StackParameters{{template_location}}.json
      stackName: {{pascalcase_name}}Stack
      changesetName: {{lowercase_name}}-changeset-${{ needs.get-changed-files.outputs.date }}
    steps:
      - name: 'Checkout'
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.sha }}
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_{{upper_short_stage}} }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_{{upper_short_stage}} }}
          aws-region: ${{ env.AWS_REGION }}
      - name: 'Create Cloudformation change set'
        run: aws cloudformation create-change-set --stack-name ${{ env.stackName }} --template-body ${{ env.templateFile }} --parameters ${{ env.parametersFile }} --capabilities CAPABILITY_NAMED_IAM --change-set-name ${{ env.changesetName }}
      - name: Sleep for 60 seconds
        run: sleep 60s
        shell: bash
      - name: 'Describe Cloudformation change set'
        id: describe_cf
        run: aws cloudformation describe-change-set --stack-name ${{ env.stackName }} --change-set-name ${{ env.changesetName }} --output table
"""

update_template = """
  update-stack-{{lowercase_name}}:
    runs-on: arm-runner
    needs: get-changed-files
    if: needs.get-changed-files.outputs.{{lowercase_name}} == 'true'
    env:
      templateFile: {{stage_folder}}/{{pascalcase_name}}Stack.yml
      parametersFile: file:///${{ github.workspace }}/{{stage_folder}}/{{pascalcase_name}}StackParameters{{template_location}}.json
      stackName: {{pascalcase_name}}Stack
    steps:
      - name: "Checkout"
        uses: actions/checkout@v3
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_{{upper_short_stage}} }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_{{upper_short_stage}} }}
          aws-region: ${{ env.AWS_REGION }}
      - name: Get task definition version number
        id: task-def
        run: |
          TASK_DEF=$(aws ecs describe-task-definition --task-definition {{camelcase_name}}-task-definition | grep revision | tr -d -c 0-9)
          echo "value=${TASK_DEF:-"1"}" >> "$GITHUB_OUTPUT"
      - name: Fill in the task definition versions
        uses: cschleiden/replace-tokens@v1
        with:
          files: {{stage_folder}}/{{pascalcase_name}}StackParameters{{template_location}}.json
        env:
          TASK_DEF: ${{ steps.task-def.outputs.value }}
      - name: Deploy to AWS CloudFormation
        uses: aws-actions/aws-cloudformation-github-deploy@v1
        with:
          name: ${{ env.stackName }}
          template: ${{ env.templateFile }}
          parameter-overrides: ${{ env.parametersFile }}
          capabilities: CAPABILITY_IAM, CAPABILITY_NAMED_IAM
"""


def replace_templates(text: str, params: TemplateParams) -> str:
    for k, v in params.items():
        text = text.replace("{{" + k + "}}", v)

    return text


def template_and_write(source_folder: str, dest_folder: str, params: TemplateParams):
    def list_files(base) -> List[str]:
        # include_hidden only added in Python 3.10
        files = glob.glob(f"templates/{base}/**/*", recursive=True)
        return files

    new_files = []
    source_list = list_files(source_folder) + list_files(source_folder + "/.github")
    for file_name in source_list:
        new_fname = replace_templates("/".join(file_name.split("/")[3:]), params)
        new_path = f"{dest_folder}/{new_fname}"

        if os.path.exists(new_path):
            logging.info(f"File/folder already exists, skipping: {new_path}")
            continue

        if os.path.isdir(file_name):
            os.makedirs(new_path)
            continue

        with open(file_name, "r") as f:
            new_text = replace_templates(f.read(), params)

        with open(new_path, "w") as new_file:
            new_file.write(new_text)
            new_files.append(new_fname)

    return new_files


def patch_gh_deploy_changeset(dest_folder: str, params: TemplateParams):
    if params["stage_folder"] == "staging":
        changeset_path = "staging-london"
    else:
        changeset_path = "prod1a"
    file_postfix = f".github/workflows/changeset-{changeset_path}.yml"
    changeset_path = f"{dest_folder}/{file_postfix}"

    with open(changeset_path, "r") as f:
        changeset = f.readlines()

    def find_line(full_text, search):
        for x in range(len(full_text)):
            if search in full_text[x]:
                return x

    change_file_idx = find_line(changeset, "date: ${{ steps.date.outputs.date }}")
    changeset.insert(
        change_file_idx + 1,
        "      "
        + params["lowercase_name"]
        + ": ${{ steps.filter.outputs."
        + params["lowercase_name"]
        + " }}\n",
    )

    filter_idx = find_line(
        changeset, f"- '{params['stage_folder']}/StripeWebhookParameters"
    )
    changeset.insert(filter_idx + 1, f"            {params['lowercase_name']}:\n")
    changeset.insert(
        filter_idx + 2,
        f"""              - '{params['lowercase_stage']}/{params['pascalcase_name']}Stack.yml'
              - '{params['lowercase_stage']}/{params['pascalcase_name']}StackParameters{params['template_location']}.json'\n""",
    )

    # template_and_write()
    changeset_text = replace_templates(changeset_template, params)
    # for changeset_line in changeset_text.
    changeset += [x + "\n" for x in changeset_text.split("\n")]

    with open(changeset_path, "w") as f:
        f.writelines(changeset)

    logging.info("CF Changeset updated")
    return [file_postfix]


def patch_gh_deploy_update(dest_folder: str, params: TemplateParams):
    if params["stage_folder"] == "staging":
        changeset_path = "staging-london"
    else:
        changeset_path = "prod1a"
    file_postfix = f".github/workflows/update-{changeset_path}.yml"
    update_path = f"{dest_folder}/{file_postfix}"
    with open(update_path, "r") as f:
        update_txt = f.readlines()

    def find_line(full_text, search):
        for x in range(len(full_text)):
            if search in full_text[x]:
                return x

    change_file_idx = find_line(
        update_txt, "websocket: ${{ steps.filter.outputs.websocket }}"
    )
    update_txt.insert(
        change_file_idx + 1,
        "      "
        + params["lowercase_name"]
        + ": ${{ steps.filter.outputs."
        + params["lowercase_name"]
        + " }}\n",
    )

    filter_idx = find_line(
        update_txt,
        f"- '{params['stage_folder']}/WebsocketApiStackParameters{params['template_location']}.json'",
    )
    update_txt.insert(filter_idx + 1, f"            {params['lowercase_name']}:\n")
    update_txt.insert(
        filter_idx + 2,
        f"""              - '{params['lowercase_stage']}/{params['pascalcase_name']}Stack.yml'
              - '{params['lowercase_stage']}/{params['pascalcase_name']}StackParameters{params['template_location']}.json'\n""",
    )

    # template_and_write()
    update_stack_entry = replace_templates(update_template, params)
    # for changeset_line in changeset_text.
    update_txt += [x + "\n" for x in update_stack_entry.split("\n")]

    with open(update_path, "w") as f:
        f.writelines(update_txt)

    logging.info("CF Update file updated")
    return [file_postfix]


def update_cf_templates(workdir: str, params: TemplateParams):
    logging.info("Cloning cloudformation-templates")
    repo = Repo.clone_from(
        "**************:blockscholes/cloudformation-templates.git", f"{workdir}/cf"
    )

    logging.info(f"Repo: {repo}")
    branch = repo.create_head(
        f"add_{params['lowercase_name']}_{params['lowercase_stage']}"
    )

    logging.info(f"New branch: {branch}")
    repo.head.reference = branch
    assert not repo.head.is_detached
    files_changed = template_and_write("create_ecs_stream/cf", f"{workdir}/cf", params)

    files_changed += patch_gh_deploy_changeset(
        dest_folder=f"{workdir}/cf", params=params
    )
    files_changed += patch_gh_deploy_update(dest_folder=f"{workdir}/cf", params=params)

    logging.info("Cloudformation files created/updated")

    repo.index.add(files_changed)
    repo.index.commit(f"Add {params['pascalcase_stage']} {params['pascalcase_name']}")

    origin = repo.remote(name="origin")
    rem_ref = RemoteReference(repo, f"refs/remotes/{origin}/{branch.name}")
    repo.head.reference.set_tracking_branch(rem_ref)
    origin.push()

    logging.info("Cloudformation templates pushed to github")
    logging.info(
        f"Create CF PR: https://github.com/blockscholes/cloudformation-templates/compare/{branch.name}"
    )


def streamify_repo(workdir: str, params: TemplateParams):
    logging.info(f"Cloning {params['repo_name']}")
    repo = Repo.clone_from(
        f"**************:blockscholes/{params['repo_name']}.git", f"{workdir}/repo"
    )

    logging.info(f"Repo: {repo}")
    branch = repo.create_head("add_streaming")
    logging.info(f"New branch: {branch}")
    repo.head.reference = branch
    assert not repo.head.is_detached
    files_changed = template_and_write(
        "create_ecs_stream/repo", f"{workdir}/repo", params
    )

    logging.info("Repo files created/updated")

    repo.index.add(files_changed)
    repo.index.commit("Add streaming version")

    origin = repo.remote(name="origin")
    rem_ref = RemoteReference(repo, f"refs/remotes/{origin}/{branch.name}")
    repo.head.reference.set_tracking_branch(rem_ref)
    origin.push()

    logging.info("Repo updates pushed to github")
    logging.info(
        f"Create Calc PR: https://github.com/blockscholes/{params['repo_name']}/compare/{branch.name}"
    )


def run(params: TemplateParams):
    with tempfile.TemporaryDirectory() as workdir:
        update_cf_templates(workdir, params=params)
        streamify_repo(workdir, params=params)


def generate_params(
    pascal_name: str, stage: Literal["staging", "prod1a"], repo_name: str
) -> TemplateParams:
    if stage == "staging":
        pascal_stage = "Staging"
        stage_folder = "staging"
        upper_short_stage = "STAG"
        cluster_name = "Staging"
        private_one_subnet = "subnet-0bf76c0844325d0c3"
        private_two_subnet = "subnet-08535a4e42b5f231d"
        vpc_id = "vpc-01e95aee0d2396215"
        template_location = "London"

        org_id = 273532302533

    elif stage == "prod1a":
        pascal_stage = "Prod1a"
        stage_folder = "prod1a"
        upper_short_stage = "PROD"
        cluster_name = "Production"
        private_one_subnet = "subnet-03c75858b57827a88"
        private_two_subnet = "subnet-0981967d4785b7405"
        vpc_id = "vpc-0d186fbdad37fd47d"
        org_id = 685767522279
        template_location = ""  # Staging is ahead of prod CFN changes

    else:
        raise NotImplementedError(f"Stage not known: {stage}")

    return dict(
        camelcase_name=pascal_name[:1].lower() + pascal_name[1:],
        pascalcase_name=pascal_name,
        lowercase_name=pascal_name.lower(),
        aws_org_id=str(org_id),
        pascalcase_stage=pascal_stage,
        stage_folder=stage_folder,
        lowercase_stage=pascal_stage.lower(),
        cluster_name=cluster_name,
        upper_short_stage=upper_short_stage,
        repo_name=repo_name,
        private_one_subnet=private_one_subnet,
        private_two_subnet=private_two_subnet,
        vpc_id=vpc_id,
        template_location=template_location,
    )


if __name__ == "__main__":
    params = generate_params(
        pascal_name="CompositeCalcStream",
        stage="staging",
        repo_name="compositeCalc",
    )

    run(params=params)
