# bscli

Command line tools for development

Currently available:

- **create_ecs_stream**: Create an ECS task (barely has a command line interface)
- **create_prod_release**: Create prod release for repo(s)
- **data_qa**: Fetch, visualise & dump market & derived data
- **fetch_client_subs**: Extract client subscription data from CloudWatch logs

## **Create ECS task**

### Prerequisites

- In order to use CLI scripts which interact with github please follow the github tutorial to add SSH keys (link found here)[https://docs.github.com/en/authentication/connecting-to-github-with-ssh/generating-a-new-ssh-key-and-adding-it-to-the-ssh-agent]

- Check the versions of internal packages within all the requirements.txt to ensure its the latest we are using elsewhere (e.g. blockstream version)

Creates an ecs task to run a streaming version of a unit of code example of how to run it

```
python bscli.py create_ecs_stream ForwardCalcStream forwardCalc staging
```

Once safely running/created do the same for prod1a

## **Prod Deploy Prep**

Options:

- `-repo` : Repo(s) to prepare for deployment. Accepts single or comma-separated list. If unspecified, all relevant repos will be prepped.
- `-log_level`: Logging severity level. Can also be set as env var (`LOG_LEVEL`). Default: `BSDEBUG`
- `-github_token`: Github token used for authenticated API calls. Can also be set as env var (`GITHUB_TOKEN`)

This automates the workflow for prod deployment preparation, documented here (_TODO doc link_). Performs a number of action steps as part of the preparation:

1. Closes outstanding automated release PRs. Those are considered stale, and a new production branch PR will be opened based on the new release.
2. Resets production branches to the last commit from `main` that was successfully deployed to production.
3. Creates a release branch dated to today (ie. `release/2023-10-25`)
4. Creates a PR from the release branch to all applicable repo production branches (ie, `release/2023-10-25` -> `production_stream`)

## **Data QA**

### Supported Data Types

data_qa supports multiple data categories, which can be configured through separate JSONC query files:

- **Implied Volatility** (iv, params)
- **Futures** (px, annualised_rate)
- **Options** (px)
- **Perpetual** (px, rate)
- **Spot** (px)

Only data saved down historically is supported at the moment. The following are also currently **not** supported:

- Open Interest
- Trading Volumes
- Constant Tenor Yields
- Realized Vol
- IV-derivates: Skew, Butterfly

### Modes of Operation

- **Plotting**  
  Generates interactive Plotly charts. Set `OUTPUT_TARGET=plot` to visualize data. Only timeseries plots are supported at the moment.

- **Data Dump**  
  Exports data to CSV files. Set `OUTPUT_TARGET=dump` to save data to disk. The output is in a normalised format: set to `dump_raw` in order to save unprocessed, in the same format as retrieved from storage - one exception to this is that rows are **always** output with their catalog instrument information. **NOTE**: `dump_raw` **_overrides_** `EXCLUDE_ZERO`.

### Configurations

#### -- **Environment Variables** --

Required

- **TARGET_CONFIG**: "iv", "px_future", "px_option", "px_perp", "px_spot". Determines the data type to query by loading up its configuration parameters from under `bscli/data_qa/query_target_configs`

- **QUERY_START**: query start datetime in ISO8601 format (ie. "2025-01-10T00:00:00Z")

- **QUERY_END**: query end datetime in ISO8601 format (ie. "2025-01-10T00:00:00Z")

- **OUTPUT_TARGET**: "plot", "dump", "dump_raw" **(default: "plot")**.

- **AWS_ENV**: "staging", "prod". Identifies AWS environment to fetch from **(default: "staging")**.

Optional

- **OUTPUT_SEPARATOR**: "tenors", "base_asset". When set, data is output separately based on this property **(default: none)**.

- **EXCLUDE_ZERO**: If set to anything other than empty string, excludes zero values from the output **(default: False)**.

- **DUMP_PATH**: Directory path for saving CSV dump outputs **(default: "bscli/data_qa//data_dumps/")**.

- **LOG_LEVEL**: Adjusts logging verbosity **(default: INFO)**.

#### -- **Target Parameters** --

- **tenors**: can include expiries in DDMMMYY format (ie. 27JUN25), constant tenors (ie. 30d), or filter object in the format `{ "expiry": { "days_lte": 180 } }`. This filter would include all **listed** expiries that satisfy the condition (days to expiry Less Than or Equal to value). **NOTE**: the filter only captures listed expiries and not constant tenors, those always have to be explicitly specified

- **freq**: all derived data is only supported at `1h`/`1m`, while _some_ market data is only supported at `tick`. **NOTE**: querying at `1m`/`tick` frequencies can involve pulling & processing _a lot_ of data - closely consider your target date range, and restrict to the minimum required for your use case

### Example Usage

Check the launch.json file for preconfigured run modes.

## **Fetch Client Subscriptions**

A tool for extracting connection subscription data from CloudWatch logs. This helps investigate/track the different connections + subscriptions a client made.

#### -- **Environment Variables** --

Required:

- **QUERY_START**: Query start datetime in ISO8601 format (ie. "2025-01-10T00:00:00Z")

- **QUERY_END**: Query end datetime in ISO8601 format (ie. "2025-01-10T00:00:00Z")

- **API_KEY_NAME_SEARCH_STRING**: String contained in API key name to search subscriptions for

Optional:

- **OUTPUT_PATH**: Directory path for saving JSON output file **(default: "bscli/fetch_client_subs/client_subs/")**

- **AWS_REGION**: AWS region **(default: "eu-west-2")**

- **AWS_ENV**: "staging", "prod". Identifies AWS environment to fetch from **(default: "prod")**.

- **SUB_SEARCH_STRING_INCLUDE**: Filter to include only messages containing this string

- **SUB_SEARCH_STRING_EXCLUDE**: Filter to exclude messages containing this string

- **LOG_LEVEL**: Adjusts logging verbosity **(default: INFO)**.

### Example Usage

Check the launch.json file for preconfigured run modes.
