#!/bin/bash

# List of base assets
BASE_ASSETS_LIST=("AVAX" "LTC" "HBAR" "BCH" "UNI" "APT" "NEAR" "DOT" "ICP" "FIL" "POL" "NXPC" "BTC" "ETH" "SOL" "XRP" "BNB" "AAVE" "ADA" "AI16Z" "AIXBT" "BERA" "DOGE" "ENA" "FARTCOIN" "IP" "JUP" "KAITO" "BONK" "SHIB" "LINK" "PENDLE" "PENGU" "POPCAT" "SUI" "TRUMP" "UXLINK" "VINE" "VIRTUAL" "WIF" "WLD" "XLM")
# Common environment variables
export AWS_ENV=prod
export AWS_PROFILE=prod
export TARGET_CONFIG=px_spot
export OUTPUT_TARGET=dump
export DUMP_PATH=/home/<USER>/PycharmProjects/bscli/data_qa/data_dumps
export QUERY_START=2025-03-01T00:00:00Z
export QUERY_END=2025-07-01T00:00:00Z

# Python script path
PYTHON_SCRIPT="/home/<USER>/PycharmProjects/bscli/bscli.py"

# Function to run the command for a single base asset
run_data_qa() {
    local base_asset=$1
    echo "Starting data_qa for $base_asset..."

    BASE_ASSETS=$base_asset python $PYTHON_SCRIPT data_qa

    if [ $? -eq 0 ]; then
        echo "✓ Completed data_qa for $base_asset"
    else
        echo "✗ Failed data_qa for $base_asset"
    fi
}

# Start all processes in parallel
echo "Starting parallel data_qa processes..."
pids=()

for asset in "${BASE_ASSETS_LIST[@]}"; do
    run_data_qa "$asset" &
    pids+=($!)
done

# Wait for all background processes to complete
echo "Waiting for all processes to complete..."
for pid in "${pids[@]}"; do
    wait $pid
done

echo "All data_qa processes completed!"
