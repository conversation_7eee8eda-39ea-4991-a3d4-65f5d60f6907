from dataclasses import dataclass
from datetime import datetime
from typing import (
    Any,
    DefaultDict,
    Literal,
    Mapping,
    NotRequired,
    Sequence,
    TypedDict,
    Union,
)

from datagrabber import CatalogAssetType

IVLevelTypes = Literal["moneyness", "delta", "strike", "params", "index"]


class IvLevel(TypedDict):
    type: IVLevelTypes
    values: list[str]


class TimeFilter(TypedDict):
    hour: int
    tz: str


class TargetFilter(TypedDict):
    time: TimeFilter


class TenorConditionFilter(TypedDict, total=False):
    days_lte: int
    days_gte: int


class TenorCondition(TypedDict):
    expiry: TenorConditionFilter


TenorItem = Union[str, TenorCondition]
ListedSmile = dict[str, Union[float, str]]


class MasterParamsBase(TypedDict):
    base_assets: list[str]
    freq: str
    start: str
    end: str
    versions: list[str]
    filter: NotRequired[TargetFilter]


# ---------------------- IV -------------------------


class ParamsIv(MasterParamsBase):
    exchanges: list[str]
    level: IvLevel
    mode: Literal["series"]
    models: list[str]
    tenors: list[TenorItem]


class TargetConfigIv(TypedDict):
    target: Literal["iv"]
    params: ParamsIv
    separator: NotRequired[Literal["tenors"]]


# ---------------------- PX --------------------------


class AssetOptionParams(TypedDict):
    asset_type: Literal["option"]
    instrument_types: list[Literal["C", "P"]]
    strikes: list[int | str]


class AssetOptionParamsClean(TypedDict):
    asset_type: Literal["option"]
    instrument_types: list[Literal["C", "P"]]
    strikes: list[str]


class AssetFutureParams(TypedDict):
    asset_type: Literal["future"]
    field: Literal["px", "annualised_rate"]


class AssetSpotParams(TypedDict):
    asset_type: Literal["spot"]


class AssetPerpParams(TypedDict):
    asset_type: Literal["perpetual"]


AssetParams = (
    AssetOptionParams
    | AssetFutureParams
    | AssetSpotParams
    | AssetPerpParams
    | AssetOptionParamsClean
)


# TODO: think about better handling funding & other non-px types
PxType = Literal["theoretical", "mid", "bid", "ask", "index", "funding", "trade"]

ExchangePxTypeMap = dict[tuple[str, ...], list[PxType]]


class AssetTransformation(TypedDict):
    output_base_asset_symbol: NotRequired[str]
    scale_factor: int


class TransformationRules(TypedDict):
    output_quote_asset: str
    transformations: dict[str, AssetTransformation]


class ParamsPx(MasterParamsBase):
    exchanges: ExchangePxTypeMap
    asset_params: AssetParams
    quote_assets: NotRequired[list[str]]
    tenors: NotRequired[list[TenorItem]]
    transformation_rules: TransformationRules


class TargetConfigPx(TypedDict):
    target: Literal["px"]
    params: ParamsPx
    separator: NotRequired[Literal["tenors"]]


class InstrDetails(TypedDict):
    exp: NotRequired[str]
    strike: NotRequired[int]
    quote_asset: str | None


# ---------------------- ANALYTICS --------------------------


class MarketMisfitConfig(TypedDict):
    target_pct: float
    window: Literal["static", "rolling"]
    metrics: list[
        Literal[
            "spread_diff_relative",
            "spread_diff_absolute",
            "spread_diff_persistence",
        ]
    ]


class FailRateConfig(TypedDict):
    pass  # TODO


class OptionAnalyticsParamsConfig(TypedDict):
    market_misfit: MarketMisfitConfig
    fail_rate: NotRequired[FailRateConfig]


class ParamsOptionAnalyticsParams(MasterParamsBase):
    exchanges: list[str]
    base_assets: list[str]
    freq: str
    analytics: OptionAnalyticsParamsConfig


class TargetConfigAnalyticsPx(TypedDict):
    target: Literal["analytics_px_option"]
    params: ParamsOptionAnalyticsParams


## ----------------------------------------------------


TargetConfig = Union[TargetConfigIv, TargetConfigPx, TargetConfigAnalyticsPx]


class CatalogItemDict(TypedDict):
    base_asset: str
    instrument: str
    exchange: str
    asset_type: NotRequired[str]
    expiry: NotRequired[str]
    listing: NotRequired[str]
    type: NotRequired[Literal["C", "P"]]
    strike: NotRequired[float]
    quote_asset: NotRequired[str]
    settlement_asset: NotRequired[str]


@dataclass(frozen=True)
class CatalogItemDataclass:
    qualified_name: str
    expiry: str
    listing: str
    type: Literal["C", "P"]
    strike: float
    base_asset: str
    quote_asset: str
    instrument: str

    @classmethod
    def from_dict(cls, d: Mapping[str, Any]) -> "CatalogItemDataclass":
        return cls(
            qualified_name=d["qualified_name"],
            expiry=d.get("expiry", ""),
            listing=d.get("listing", ""),
            type=d.get("type", ""),
            strike=float(d.get("strike", 0.0)),
            base_asset=d.get("baseAsset", ""),
            quote_asset=d.get("quoteAsset", ""),
            instrument=d.get("instrument", ""),
        )

    def to_dict(self) -> CatalogItemDict:
        catalog_qn_tokens = self.qualified_name.split(".")
        return CatalogItemDict(
            expiry=self.expiry,
            listing=self.listing,
            type=self.type,
            strike=self.strike,
            base_asset=self.base_asset,
            quote_asset=self.quote_asset,
            instrument=self.instrument,
            exchange=catalog_qn_tokens[0],
            asset_type=catalog_qn_tokens[1],
        )


class DynamoItem(TypedDict):
    timestamp: int
    qualified_name: str


class DynamoItemWithCatalogDetails(DynamoItem, CatalogItemDict, total=False):
    pass


# TODO: refactor this to be more general with respect to all exchange items
class ExchangePxItem(DynamoItemWithCatalogDetails, total=False):
    px: float
    rate: float


class TOptionPxItem(DynamoItemWithCatalogDetails):
    pxs: str


class TFuturePxItem(DynamoItemWithCatalogDetails):
    px: str
    annualised_rate: float


PxItem = ExchangePxItem | TOptionPxItem | TFuturePxItem


IvItem = dict[str, Any]
Scalar = Union[int, float, str]

Snap = IvItem | ExchangePxItem | TOptionPxItem | TFuturePxItem

# IV: version.exchange.expiry.level_v -> { dt -> val }
# PX:
# - option: version.exchange.instrument.suffix -> { dt -> val }
#           exchange.freq.instrument.suffix -> { dt -> val }
PreppedData = DefaultDict[str, dict[datetime, Scalar]]


class RetrieveData(TypedDict):
    conversion_data: Sequence[Snap]
    raw_data: Sequence[Snap]


class QnTokens(TypedDict):
    exchange: str
    asset_type: CatalogAssetType
    version: str
    suf: Literal["px", "pxs"]
    pxtype: str


class GetFieldValueParams(TypedDict):
    pxtype: PxType
    row: PxItem
    suf: Literal["px", "pxs"]
    asset_type: CatalogAssetType
    it: NotRequired[str]
    strike: NotRequired[str]
    field: NotRequired[Literal["px", "annualised_rate"]]


class UpdateOutputParams(TypedDict):
    pxtype: PxType
    asset_type_param: CatalogAssetType
    exchange: str
    freq: Literal["1m", "1h"]
    version: str
    dt: datetime
    exp: str | None
    quote_asset: str | None
    base_asset: str
    field_name: Literal["px", "annualised_rate"]
    field_value: float
    tenor: NotRequired[str]
    it: NotRequired[Literal["C", "P"]]
    strike: NotRequired[Scalar]


class MarketMisfitParams(TypedDict):
    theoretical: float
    bid: float
    ask: float
