import ast
import json
import math
import multiprocessing
import os
from collections.abc import Collection, Mapping
from datetime import datetime, timezone
from typing import Any, Literal, Optional, cast

import utils_general

from datagrabber import (
    CatalogAssetType,
)

from .config import SECONDS_PER_DAY
from .typings import PxItem, QnTokens, Scalar, TFuturePxItem, TOptionPxItem


def get_num_required_workers(data: Collection[Any]) -> int:
    return min(
        int(os.environ.get("NUM_WORKERS", multiprocessing.cpu_count() * 2)),
        math.ceil(len(data) / 150000),
    )


def to_iso(date: str) -> str:
    return datetime.strptime(date.zfill(7), "%d%b%y").strftime("%Y-%m-%dT08:00:00Z")


def get_days_to_expiry(exp: str, ref_ts: int) -> float:
    exp_date = datetime.strptime(exp, "%d%b%y").replace(hour=8, tzinfo=timezone.utc)
    ref_date = utils_general.to_datetime(ref_ts)
    delta = exp_date - ref_date
    return delta.total_seconds() / SECONDS_PER_DAY


def get_decimal_places(number: float) -> int:
    # Convert to string and count decimal places
    s = f"{number:.9f}".rstrip("0")  # Format to max precision and strip trailing zeroes
    if "." in s:
        return len(s.split(".")[1])
    return 0


def convert_to_valid_json(data: str) -> str:
    # Safely evaluate the string as a Python literal
    parsed_data = ast.literal_eval(data)
    # Convert the Python object to a JSON-formatted string
    return json.dumps(parsed_data)


def get_pxs_dict(row: TOptionPxItem) -> Mapping[Literal["C", "P"], Mapping[str, float]]:
    pxs_data = row["pxs"]
    if isinstance(pxs_data, str):  # type: ignore not consistently str
        converted = convert_to_valid_json(pxs_data)
        return utils_general.json_loads(converted)
    return pxs_data


def parse_qualified_name(qn: str) -> QnTokens:
    version, qn_tokens = utils_general.get_qfn_and_version(qn)
    return {
        "exchange": qn_tokens[0],
        "asset_type": cast(CatalogAssetType, qn_tokens[1]),
        "version": version if version else "",
        "suf": cast(Literal["px", "pxs"], qn_tokens[-1]),
        "pxtype": (
            qn_tokens[-2] if qn_tokens[-2] not in ["tick", "1m", "1h"] else "trade"
        ),
    }


def get_field_value(
    pxtype: str,
    row: PxItem,
    suf: Literal["px", "pxs", "rate"],
    asset_type: CatalogAssetType,
    it: Optional[Literal["C", "P"]] = None,
    strike: Optional[Scalar] = None,
    field: Literal["px", "annualised_rate", "rate"] = "px",
) -> float:
    if pxtype == "theoretical":
        if asset_type == "future":
            assert field in ("annualised_rate", "px")
            row = cast(TFuturePxItem, row)
            return float(row[field])

        elif asset_type == "option":
            if "px" in row:
                return float(
                    cast(
                        float,
                        row["px"][0] if isinstance(row["px"], list) else row["px"],
                    )
                )
            assert strike is not None
            assert it is not None
            pxs = get_pxs_dict(cast(TOptionPxItem, row))
            return float(pxs[it][str(float(strike))])

        else:
            raise ValueError(f"Invalid set of params {asset_type=}, {pxtype=}")

    elif pxtype in ["index", "mid", "bid", "ask", "trade"]:
        assert suf == "px" and suf in row
        return float(row[suf])

    elif pxtype == "funding":
        assert suf == "rate" and suf in row
        return float(row[suf])
    else:
        raise ValueError(f"Invalid pxtype: {pxtype}")
