{
  "exchanges": {
    // "blockscholes": ["index"]
    // "deribit,bybit,okx,binance": ["bid", "ask"],
    "deribit,bybit,okx,binance,bitget": ["funding"]
  },
  "base_assets": [
    // "ARB",
    // "ATOM",
    // "BNB",
    "BTC",
    // "ETH",
    // "OP",
    // "ICP",
    // "POL",
    // "SOL",
    // "TON",
    // "ZK",
    // "SOL"
  ],
  "asset_params": {
    "asset_type": "perpetual",
    // "field": "px"
    "field": "rate"
  },
  "transformation_rules": {
    // "output_quote_asset": "USD"
    // "transformations": {
    //   "PEPE": {
    //     "output_base_asset_symbol": "1000PEPE",
    //     "scale_factor": 1000
    //   }
    // }
  },
  "freq": "1h"
}
