{
  "exchanges": {
    // "bitget": ["bid", "ask", "trade"]
    // "okx,cryptocom,coinbase,binance,bitstamp,bitfinex,bybit,kraken": ["mid"],
    "blockscholes": ["index"]
    // "okx": ["trade"]
//    "v3uniswap-ethereum,kyberswap-ethereum,kyberswap-bsc,curve-ethereum,oneinch-ethereum,zerox-ethereum,jupiter-solana": [
//      "mid"
//    ]
  },
  "base_assets": [
     "USDT",
     "USDC",
    // "USDC"
    // "BITCOIN"
    // "HPOS10I"
    // "SEI"
    // "AUSD",
    // "DAI",
    // "PYUSD",
    // "SDAI",
    // "SUSDE"
    // "USDE"
    // "CBBTC"
    // "BITCOIN"
    // "EBTC"
    // "LBTC"
    // "WBTC"
    // "SOLVBTC"
    // "SOLVBTC-BBN"
    // "AGETH",
    // "EZETH",
    // "PUFETH",
    // "RSETH"
    // "RSWETH",
    // "WEETH",
    // "WSTETH"
    // "ARB"
    // "XRP"
    // "ATOM",
//     "BNB",
    // "BTC",
    // "ETH",
    // "OP",
    // "PEPE",
    // "POL",
    // "SOL",
    // "TON",
    // "ZK"
//    "OLAS"
  ],
  "quote_assets": ["USD"],

  "asset_params": {
    "asset_type": "spot"
  },
  "transformation_rules": {
    "output_quote_asset": "USD"
    //   "transformations": {
    //     "PEPE": {
    //       "output_base_asset_symbol": "1000PEPE",
    //       "scale_factor": 1000
    //     }
    //   }
  },
  "freq": "1h"
}
