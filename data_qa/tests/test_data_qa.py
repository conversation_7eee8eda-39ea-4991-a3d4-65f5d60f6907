from typing import Any, cast

import pytest

from ..data_qa import _get_separator_value  # type: ignore


def test_get_separator_value() -> None:
    """Test the get_separator_value function with various key formats and separator types."""

    # Test tenors separator cases
    assert (
        _get_separator_value("v-00004.deribit.BTC.spline.31Dec24.5delta.iv", "tenors")
        == "2024-12-31T08:00:00Z"
    )
    assert (
        _get_separator_value("v-00004.deribit.BTC.spline.7d.5delta.iv", "tenors")
        == "7d"
    )
    assert (
        _get_separator_value("v-00004.deribit.BTC.spline.1m.5delta.iv", "tenors")
        == "1m"
    )
    assert (
        _get_separator_value("deribit.BTC_USD.31Dec24.theoretical.px", "tenors")
        == "2024-12-31T08:00:00Z"
    )
    assert (
        _get_separator_value("deribit.BTC_USD.31Dec24-25000-C.mid.px", "tenors")
        == "2024-12-31T08:00:00Z"
    )
    assert (
        _get_separator_value("deribit.BTC_USD.31Dec24-25000-C.mid.px", "tenors")
        == "2024-12-31T08:00:00Z"
    )

    # Test base assets separator cases
    assert (
        _get_separator_value("deribit.BTC_USD.31Dec24.theoretical.px", "base_assets")
        == "BTC"
    )
    assert (
        _get_separator_value("deribit.ETH_USDT.31Dec24-2000-P.mid.px", "base_assets")
        == "ETH"
    )
    assert (
        _get_separator_value(
            "v-00004.deribit.SOL.spline.31Dec24.5delta.iv", "base_assets"
        )
        == "SOL"
    )


def test_get_separator_value_errors() -> None:
    """Test error cases for get_separator_value function."""

    with pytest.raises(ValueError, match="Unsupported separator: invalid"):
        _get_separator_value("some.key", cast(Any, "invalid"))
