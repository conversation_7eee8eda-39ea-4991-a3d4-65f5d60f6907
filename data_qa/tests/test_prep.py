from datetime import datetime
from typing import cast

import pytest

from ..prep import (
    _get_smile_expiry,  # type: ignore
)
from ..typings import (
    ListedSmile,
    ParamsIv,
    TenorCondition,
)


@pytest.mark.parametrize(  # type: ignore
    "params,smile,row,expected",
    [
        # Direct tenor match
        (
            cast(ParamsIv, {"tenors": ["01NOV24"]}),
            cast(ListedSmile, {"expiry_iso": "2024-11-01T08:00:00Z"}),
            {
                "timestamp": int(datetime(2024, 11, 1, 8, 0).timestamp() * 1e9)
            },  # 2024-11-01T08:00:00Z
            "01NOV24",
        ),
        # Days to expiry condition match - fixed timestamp to be 7 days before expiry
        (
            cast(
                ParamsIv,
                {"tenors": [cast(TenorCondition, {"expiry": {"days_lte": 7}})]},
            ),
            cast(ListedSmile, {"expiry_iso": "2024-11-07T08:00:00Z"}),
            {
                "timestamp": int(datetime(2024, 10, 31, 8, 0).timestamp() * 1e9)
            },  # 2024-10-31T08:00:00Z
            "07NOV24",
        ),
        # No match - expiry beyond days_lte condition
        (
            cast(
                ParamsIv,
                {"tenors": [cast(TenorCondition, {"expiry": {"days_lte": 7}})]},
            ),
            cast(ListedSmile, {"expiry_iso": "2024-12-01T08:00:00Z"}),
            {
                "timestamp": int(datetime(2024, 10, 31, 8, 0).timestamp() * 1e9)
            },  # 2024-10-31T08:00:00Z
            "",
        ),
        # Using expiry_str instead of expiry_iso
        (
            cast(ParamsIv, {"tenors": ["01NOV24"]}),
            cast(ListedSmile, {"expiry_str": "2024-11-01T08:00:00Z"}),
            {
                "timestamp": int(datetime(2024, 11, 1, 8, 0).timestamp() * 1e9)
            },  # 2024-11-01T08:00:00Z
            "01NOV24",
        ),
        # No match - different tenor
        (
            cast(ParamsIv, {"tenors": ["02NOV24"]}),
            cast(ListedSmile, {"expiry_iso": "2024-11-01T08:00:00Z"}),
            {
                "timestamp": int(datetime(2024, 11, 1, 8, 0).timestamp() * 1e9)
            },  # 2024-11-01T08:00:00Z
            "",
        ),
    ],
)
def test__get_smile_expiry(
    params: ParamsIv,
    smile: ListedSmile,
    row: dict[str, int],
    expected: str,
) -> None:
    """Test _get_smile_expiry function with various scenarios"""
    result = _get_smile_expiry(params, smile, row)
    assert (
        result == expected
    ), f"Expected {expected}, but got {result}. Params: {params}, Smile: {smile}"
