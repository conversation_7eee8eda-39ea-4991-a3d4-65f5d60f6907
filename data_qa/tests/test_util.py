from datetime import datetime, timezone
from typing import Any, Literal, TypedDict

import pytest
from pytest_mock import Mo<PERSON>Fixture

from datagrabber import CatalogAssetType

from ..typings import PxItem, QnTokens, TOptionPxItem
from ..util import (
    convert_to_valid_json,
    get_days_to_expiry,
    get_decimal_places,
    get_field_value,
    get_pxs_dict,
    parse_qualified_name,
    to_iso,
)


def test_to_iso() -> None:
    # Test standard cases
    assert to_iso("1Jan21") == "2021-01-01T08:00:00Z"
    assert to_iso("31Dec20") == "2020-12-31T08:00:00Z"
    assert to_iso("01Feb19") == "2019-02-01T08:00:00Z"

    # Test single-digit days with leading zeros
    assert to_iso("01Jan21") == "2021-01-01T08:00:00Z"
    assert to_iso("02Jan21") == "2021-01-02T08:00:00Z"
    assert to_iso("09Jan21") == "2021-01-09T08:00:00Z"

    # Test different months
    assert to_iso("15Mar21") == "2021-03-15T08:00:00Z"
    assert to_iso("15Apr21") == "2021-04-15T08:00:00Z"
    assert to_iso("15May21") == "2021-05-15T08:00:00Z"
    assert to_iso("15Jun21") == "2021-06-15T08:00:00Z"
    assert to_iso("15Jul21") == "2021-07-15T08:00:00Z"
    assert to_iso("15Aug21") == "2021-08-15T08:00:00Z"
    assert to_iso("15Sep21") == "2021-09-15T08:00:00Z"
    assert to_iso("15Oct21") == "2021-10-15T08:00:00Z"
    assert to_iso("15Nov21") == "2021-11-15T08:00:00Z"
    assert to_iso("15Dec21") == "2021-12-15T08:00:00Z"

    # Test year transitions
    assert to_iso("31Dec99") == "1999-12-31T08:00:00Z"
    assert to_iso("01Jan00") == "2000-01-01T08:00:00Z"


def test_get_days_to_expiry(mocker: MockerFixture) -> None:
    """Test the get_days_to_expiry function with various date scenarios."""
    # Mock reference date to 2021-01-01 00:00:00 UTC
    mocker.patch(
        "utils_general.to_datetime",
        return_value=datetime(2021, 1, 1, tzinfo=timezone.utc),
    )

    def assert_days_close(expiry: str, timestamp: int, expected: float) -> None:
        """Helper to assert floating point days are close enough"""
        result = get_days_to_expiry(expiry, timestamp)
        assert abs(result - expected) < 1e-10, f"Expected {expected}, got {result}"

    # Test standard cases - note that all dates are at 8am UTC
    assert_days_close(
        "1Jan21", 1609459200, 0.3333333333333333
    )  # 2021-01-01T08:00:00Z is 8 hours (1/3 day) after reference
    assert_days_close("2Jan21", 1609459200, 1.3333333333333333)  # 2021-01-02T08:00:00Z
    assert_days_close("1Feb21", 1609459200, 31.333333333333332)  # 2021-02-01T08:00:00Z

    # Test negative days (past dates)
    assert_days_close(
        "31Dec20", 1609459200, -0.6666666666666666
    )  # 2020-12-31T08:00:00Z is 16 hours (2/3 day) before reference
    assert_days_close(
        "30Dec20", 1609459200, -1.6666666666666667
    )  # 2020-12-30T08:00:00Z

    # Test longer time spans
    assert_days_close("1Jan22", 1609459200, 365.3333333333333)  # 2022-01-01T08:00:00Z
    assert_days_close("1Jul21", 1609459200, 181.33333333333334)  # 2021-07-01T08:00:00Z

    # Test leap year dates - note that 2024 is a leap year
    assert_days_close("29Feb24", 1609459200, 1154.3333333333333)  # 2024-02-29T08:00:00Z

    # Test month ends
    assert_days_close("31Mar21", 1609459200, 89.33333333333333)  # 2021-03-31T08:00:00Z
    assert_days_close("30Apr21", 1609459200, 119.33333333333333)  # 2021-04-30T08:00:00Z
    assert_days_close("31May21", 1609459200, 150.33333333333334)  # 2021-05-31T08:00:00Z


def test_get_decimal_places() -> None:
    # Test standard cases
    assert get_decimal_places(123.456) == 3
    assert get_decimal_places(123.456000) == 3
    assert get_decimal_places(123.000) == 0
    assert get_decimal_places(123.456789123) == 9

    # Test whole numbers
    assert get_decimal_places(123) == 0
    assert get_decimal_places(0) == 0
    assert get_decimal_places(1000) == 0

    # Test small decimals
    assert get_decimal_places(0.1) == 1
    assert get_decimal_places(0.01) == 2
    assert get_decimal_places(0.001) == 3

    # Test trailing zeros
    assert get_decimal_places(123.4500) == 2
    assert get_decimal_places(123.450000) == 2
    assert get_decimal_places(123.0) == 0

    # Test scientific notation
    assert get_decimal_places(1.23e-4) == 6
    assert get_decimal_places(1.23e4) == 0

    # Test negative numbers
    assert get_decimal_places(-123.456) == 3
    assert get_decimal_places(-0.001) == 3


# Qualified name parsing tests
@pytest.mark.parametrize(
    "qn,expected",
    [
        (
            "v-00004.deribit.option.BTC-20NOV23-25000-C.1h.theoretical.pxs",
            {
                "version": "v-00004",
                "exchange": "deribit",
                "asset_type": "option",
                "pxtype": "theoretical",
                "suf": "pxs",
            },
        ),
        (
            "deribit.option.BTC-20NOV23-25000-C.1h.mid.px",
            {
                "version": "",
                "exchange": "deribit",
                "asset_type": "option",
                "pxtype": "mid",
                "suf": "px",
            },
        ),
        (
            "blockscholes.future.BTC_USD_2023-11-20T08:00:00Z.1h.theoretical.px",
            {
                "version": "",
                "exchange": "blockscholes",
                "asset_type": "future",
                "pxtype": "theoretical",
                "suf": "px",
            },
        ),
        (
            "v-00002.okx.spot.BTC-USD.1m.index.px",
            {
                "version": "v-00002",
                "exchange": "okx",
                "asset_type": "spot",
                "pxtype": "index",
                "suf": "px",
            },
        ),
    ],
)
def test_parse_qualified_name(qn: str, expected: QnTokens) -> None:
    assert parse_qualified_name(qn) == expected


# PXS JSON parsing tests
@pytest.mark.parametrize(
    "row,expected",
    [
        (
            {"pxs": "{'C': {'25000': 0.5}}"},
            {"C": {"25000": 0.5}},
        ),
        (
            {"pxs": "{'C': {'25000': 0.5}, 'P': {'25000': 0.3}}"},
            {"C": {"25000": 0.5}, "P": {"25000": 0.3}},
        ),
        (
            {"pxs": {"C": {"25000": 0.5}}},  # Already a dict
            {"C": {"25000": 0.5}},
        ),
        (
            {"pxs": "{'C': {}}"},  # Empty strikes
            {"C": {}},
        ),
        (
            {"pxs": "{}"},  # Empty data
            {},
        ),
    ],
)
def test_get_pxs_dict(row: TOptionPxItem, expected: dict[str, Any]) -> None:
    assert get_pxs_dict(row) == expected


# Convert to valid JSON tests
@pytest.mark.parametrize(
    "input_data,expected",
    [
        ("{'key': 'value'}", '{"key": "value"}'),
        ("{1.0: 2.0, 3.0: 4.0}", '{"1.0": 2.0, "3.0": 4.0}'),
        ('{"key": "value"}', '{"key": "value"}'),
        ("{'nested': {'inner': 1.0}}", '{"nested": {"inner": 1.0}}'),
        ("{'list': [1.0, 2.0, 3.0]}", '{"list": [1.0, 2.0, 3.0]}'),
        ("{}", "{}"),
        ("{'special\\nchars': 'test'}", '{"special\\nchars": "test"}'),
        ("{'quotes\"': \"'mixed'\"}", '{"quotes\\"": "\'mixed\'"}'),
    ],
)
def test_convert_to_valid_json(input_data: str, expected: str) -> None:
    assert convert_to_valid_json(input_data) == expected


# Field value extraction tests
class GetFieldValueTestParams(TypedDict):
    pxtype: str
    row: PxItem
    suf: Literal["px", "pxs"]
    asset_type: CatalogAssetType
    it: Literal["C", "P"] | None
    strike: str | None
    field: Literal["px", "annualised_rate"]


@pytest.mark.parametrize(  # type: ignore
    "pxtype,row,suf,asset_type,it,strike,field,expected",
    [
        # Future theoretical prices
        (
            "theoretical",
            {"px": "100.0", "annualised_rate": 0.05},
            "px",
            "future",
            None,
            None,
            "px",
            100.0,
        ),
        (
            "theoretical",
            {"px": "100.0", "annualised_rate": 0.05},
            "px",
            "future",
            None,
            None,
            "annualised_rate",
            0.05,
        ),
        # Option theoretical prices
        (
            "theoretical",
            {"pxs": "{'C': {'25000.0': 0.5, '26000.0': 0.4}}"},
            "pxs",
            "option",
            "C",
            "25000.0",
            "px",
            0.5,
        ),
        (
            "theoretical",
            {"pxs": "{'P': {'25000.0': 0.3}}"},
            "pxs",
            "option",
            "P",
            "25000.0",
            "px",
            0.3,
        ),
        # Market prices
        (
            "mid",
            {"px": "100.0"},
            "px",
            "spot",
            None,
            None,
            "px",
            100.0,
        ),
        (
            "bid",
            {"px": "99.5"},
            "px",
            "spot",
            None,
            None,
            "px",
            99.5,
        ),
        (
            "ask",
            {"px": "100.5"},
            "px",
            "spot",
            None,
            None,
            "px",
            100.5,
        ),
        (
            "index",
            {"px": "100.0"},
            "px",
            "spot",
            None,
            None,
            "px",
            100.0,
        ),
    ],
)
def test_get_field_value(
    pxtype: str,
    row: PxItem,
    suf: Literal["px", "pxs"],
    asset_type: CatalogAssetType,
    it: Literal["C", "P"] | None,
    strike: str | None,
    field: Literal["px", "annualised_rate"],
    expected: float,
) -> None:
    result = get_field_value(
        pxtype=pxtype,
        row=row,
        suf=suf,
        asset_type=asset_type,
        it=it,
        strike=strike,
        field=field,
    )
    assert result == expected


def test_get_field_value_errors() -> None:
    # Test invalid pxtype
    with pytest.raises(ValueError, match="Invalid pxtype: invalid"):
        get_field_value(
            pxtype="invalid",
            row={},  # type: ignore
            suf="px",
            asset_type="spot",
        )

    # Test missing strike for option
    with pytest.raises(AssertionError):
        get_field_value(
            pxtype="theoretical",
            row={"pxs": "{'C': {'25000': 0.5}}"},  # type: ignore
            suf="pxs",
            asset_type="option",
            it="C",
            strike=None,
        )

    # Test missing instrument type for option
    with pytest.raises(AssertionError):
        get_field_value(
            pxtype="theoretical",
            row={"pxs": "{'C': {'25000': 0.5}}"},  # type: ignore
            suf="pxs",
            asset_type="option",
            it=None,
            strike="25000",
        )
