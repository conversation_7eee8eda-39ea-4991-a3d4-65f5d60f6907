from collections import defaultdict
from datetime import datetime
from typing import TypedDict

from .typings import MarketMisfitParams, PreppedData, Scalar

BASIS_POINTS_MULTIPLIER = 10000  # 100% = 10000 bps


class MarketMisfitPoint(TypedDict):
    theoretical: float
    bid: float
    ask: float
    spread_diff_relative: float


AnalyticResults = dict[str, dict[datetime, MarketMisfitPoint]]


def _calculate_spread_diff_relative(params: MarketMisfitParams) -> float:
    """
    Calculate relative difference between theoretical price and bid/ask spread in basis points.

    Returns:
        float: Relative difference in basis points. 0 if theoretical is within spread.
    """
    if params["theoretical"] < params["bid"]:
        return (params["theoretical"] / params["bid"] - 1) * BASIS_POINTS_MULTIPLIER
    elif params["theoretical"] > params["ask"]:
        return (params["theoretical"] / params["ask"] - 1) * BASIS_POINTS_MULTIPLIER
    return 0.0


def _extract_price_components(key: str) -> tuple[str, str, str, str]:
    """Extract exchange, asset, instrument details, and price type from a key."""
    tokens = key.split(".")
    exchange = tokens[0]
    asset = tokens[1]
    instr = tokens[2]  # Contains expiry, strike, type info
    price_type = tokens[-2]  # theoretical, bid, ask
    return exchange, asset, instr, price_type


def analyze_market_misfit(data: PreppedData) -> PreppedData:
    """Process data to calculate market misfit analytics."""
    # Group data by exchange, asset, and instrument
    grouped_data: dict[tuple[str, str, str], dict[str, dict[datetime, Scalar]]] = (
        defaultdict(dict)
    )

    for key, time_series in data.items():
        exchange, asset, instr, price_type = _extract_price_components(key)
        if price_type in ["theoretical", "bid", "ask"]:
            group_key = (exchange, asset, instr)
            grouped_data[group_key][price_type] = time_series

    # Calculate analytics
    results: PreppedData = defaultdict(dict)

    for (exchange, asset, instr), price_data in grouped_data.items():
        if not all(k in price_data for k in ["theoretical", "bid", "ask"]):
            continue

        theoretical_data = price_data["theoretical"]
        bid_data = price_data["bid"]
        ask_data = price_data["ask"]

        # Find common timestamps
        timestamps = (
            set(theoretical_data.keys()) & set(bid_data.keys()) & set(ask_data.keys())
        )

        for ts in timestamps:
            theoretical = float(theoretical_data[ts])
            bid = float(bid_data[ts])
            ask = float(ask_data[ts])

            spread_diff = _calculate_spread_diff_relative(
                {
                    "theoretical": theoretical,
                    "bid": bid,
                    "ask": ask,
                }
            )

            # Store result using a consistent key format
            result_key = (
                f"{exchange}.{asset}.{instr}.market_misfit.spread_diff_relative"
            )
            results[result_key][ts] = spread_diff

    return results
