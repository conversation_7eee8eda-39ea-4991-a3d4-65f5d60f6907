import copy
import functools
import logging
import math
import time
from datetime import datetime, timedelta
from multiprocessing.connection import Connection
from typing import Any, Literal, Sequence, cast

import pytz
import utils_general
from utils_general import AssetTypes, OptionTypes, parallel_process, to_datetime

from datagrabber import (
    CatalogAssetType,
    GrabParams,
    construct_timeseries_queries,
    get_instruments_async,
    grab_async,
)

from .config import LEVEL_TYPE_TO_QN_SUFFIX, QUERY_END, QUERY_START
from .typings import (
    AssetOptionParams,
    AssetParams,
    CatalogItemDataclass,
    CatalogItemDict,
    ExchangePxTypeMap,
    IVLevelTypes,
    ParamsPx,
    PxItem,
    PxType,
    QnTokens,
    RetrieveData,
    Snap,
    TargetConfig,
    TargetFilter,
    TenorCondition,
    TenorItem,
    TOptionPxItem,
)
from .util import (
    get_days_to_expiry,
    get_field_value,
    get_num_required_workers,
    get_pxs_dict,
    parse_qualified_name,
    to_iso,
)


def _generate_filter_timestamps(
    start_str: str, end_str: str, target_filter: TargetFilter
) -> set[int]:
    assert "time" in target_filter
    assert "hour" in target_filter["time"]

    start_dt = to_datetime(start_str)
    end_dt = to_datetime(end_str)
    tz = pytz.timezone(target_filter["time"]["tz"])
    hour = target_filter["time"]["hour"]

    timestamps: set[int] = set()
    current_dt = start_dt
    while current_dt <= end_dt:
        date = current_dt.date()
        naive_time = datetime.combine(date, datetime.min.time()).replace(hour=hour)
        try:
            # Attempt to localize time, catching DST transitions
            local_dt = tz.localize(naive_time, is_dst=None)
            utc_dt = local_dt.astimezone(pytz.utc)
            timestamp = int(utc_dt.timestamp() * 1e9)
            timestamps.add(timestamp)
        except pytz.NonExistentTimeError:
            logging.warning(
                f"Non-existent time encountered at DST start, skipping snapshot. {target_filter=}, {naive_time=}, {current_dt=}"
            )
        except pytz.AmbiguousTimeError:
            logging.warning(
                f"Time is ambiguous at DST end, including both DST and standard time snapshots. {target_filter=}, {naive_time=}, {current_dt=}"
            )

            local_dt_dst = tz.localize(naive_time, is_dst=True)
            utc_dt_dst = local_dt_dst.astimezone(pytz.utc)
            timestamp_dst = int(utc_dt_dst.timestamp() * 1e9)
            timestamps.add(timestamp_dst)
            # Second occurrence (Standard Time)
            local_dt_std = tz.localize(naive_time, is_dst=False)
            utc_dt_std = local_dt_std.astimezone(pytz.utc)
            timestamp_std = int(utc_dt_std.timestamp() * 1e9)
            timestamps.add(timestamp_std)
        current_dt += timedelta(days=1)
    return timestamps


def _construct_qns_iv(
    tenors: Sequence[TenorItem],
    exchanges: Sequence[str],
    versions: Sequence[str],
    level_type: IVLevelTypes,
    models: Sequence[str],
    currencies: Sequence[str],
    freq: str,
) -> list[str]:
    versioned_exchanges = [
        f"{v + '.' if v else v}{e}" for e in exchanges for v in versions
    ]
    tenors_s = set(
        [
            f"{t if (isinstance(t, str) and (t.endswith('d') or t.endswith('m'))) else 'listed'}"
            for t in tenors
        ]
    )
    if level_type == "index":
        suf = "index.vol"
    else:
        suf = LEVEL_TYPE_TO_QN_SUFFIX[level_type]

    qns: list[str] = []
    for m in models:
        for e in versioned_exchanges:
            m_str = f".{m}"
            if level_type == "index":
                e = e.replace("v2composite", "composite")
                m_str = ""
            qns.extend(
                [
                    f"{e}.option.{c}{m_str}.{t}.{freq}.{suf}"
                    for t in tenors_s
                    for c in currencies
                ]
            )
    return qns


def to_exchange_instrument(
    exch: str,
    asset_type: Literal["option", "spot", "future"],
    base_asset: str,
    exp: str | None = None,
    strike: str | None = None,
    it: OptionTypes | None = None,
):
    # Handle versioned exchange names
    if "." in exch:
        unversioned_exch = exch.split(".")[1]
    else:
        unversioned_exch = exch

    if asset_type == "option":
        assert exp is not None
        if unversioned_exch == "deribit":
            assert it is not None and strike is not None
            quote_asset = "" if base_asset in ["BTC", "ETH"] else "_USDC"
            return f"{base_asset}{quote_asset}-{exp.lstrip('0')}-{strike}-{it.value}"
        elif unversioned_exch == "okx":
            assert it is not None and strike is not None
            return f"{base_asset}-USD-{utils_general.to_datetime(to_iso(exp)).strftime('%y%m%d')}-{strike}-{it.value}"
        elif unversioned_exch == "bybit":
            assert it is not None and strike is not None
            return f"{base_asset}_USDC-{exp.lstrip('0')}-{strike}-{it.value}"
        elif unversioned_exch == "v2lyra":
            assert it is not None and strike is not None
            return f"{base_asset}-{to_iso(exp).split('T')[0].replace('-', '')}-{strike}-{it.value}"
        elif unversioned_exch == "blockscholes":
            return f"{base_asset}_USD_{to_iso(exp)}"
        else:
            raise ValueError(f"Unsupported exchange: {unversioned_exch}")

    elif asset_type == "future":
        assert exp is not None
        if unversioned_exch == "deribit":
            return f"{base_asset}-{exp.lstrip('0')}"
        elif unversioned_exch == "okx":
            return f"{base_asset}-USD-{utils_general.to_datetime(to_iso(exp)).strftime('%y%m%d')}"
        elif unversioned_exch == "bybit":
            return f"{base_asset}_USDC-{exp.lstrip('0')}"
        elif unversioned_exch == "blockscholes":
            return utils_general.generate_bs_instrument_name(
                AssetTypes(asset_type), base_asset, "USD", to_iso(exp)
            )
        else:
            raise ValueError(f"Unsupported exchange: {unversioned_exch}")

    elif asset_type == "spot":
        if unversioned_exch == "blockscholes":
            return f"{base_asset}_USD"
        else:
            raise ValueError(f"Unsupported exchange: {unversioned_exch}")
    else:
        raise ValueError(f"Unsupported asset type: {asset_type}")


def _get_exchange_pxtype_pairs(
    exchanges: ExchangePxTypeMap,
) -> set[tuple[str, PxType]]:
    """Generate pairs of (exchange, pxtype) from exchange-pxtype map."""
    pairs: set[tuple[str, PxType]] = set()
    for exch_key, pxtypes in exchanges.items():
        for exch in exch_key:
            for pxt in pxtypes:
                pairs.add((exch, pxt))
    return pairs


def _is_catalog_item_expiry_in_bounds(
    tenor_cond: TenorCondition, catalog_item: CatalogItemDataclass, start: str, end: str
) -> bool:
    expiry = catalog_item.expiry
    days_between_expiry_and_start = abs(
        get_days_to_expiry(
            utils_general.to_datetime(start).strftime("%d%b%y").upper(),
            int(utils_general.to_datetime(expiry).timestamp() * 1e9),
        )
    )
    expires_in_period_and_satisfies_cond = (
        (start <= expiry <= end)
        and (
            days_between_expiry_and_start
            <= tenor_cond["expiry"].get("days_lte", math.inf)
        )
        and (
            days_between_expiry_and_start
            >= tenor_cond["expiry"].get("days_gte", -math.inf)
        )
    )
    days_between_expiry_and_end = abs(
        get_days_to_expiry(
            utils_general.to_datetime(end).strftime("%d%b%y").upper(),
            int(utils_general.to_datetime(expiry).timestamp() * 1e9),
        )
    )
    expires_after_period_and_satisfies_cond = (
        (expiry > end)
        and (
            days_between_expiry_and_end
            <= tenor_cond["expiry"].get("days_lte", math.inf)
        )
        and (
            days_between_expiry_and_end
            >= tenor_cond["expiry"].get("days_gte", -math.inf)
        )
    )

    return (
        expires_in_period_and_satisfies_cond or expires_after_period_and_satisfies_cond
    )


def _pxtype_suffix(pxtype: PxType, asset_type: str) -> str:
    if pxtype == "theoretical":
        if asset_type == "option":
            return "pxs"
        elif asset_type == "future":
            return "px"
        else:
            raise ValueError(f"Invalid asset type: {asset_type}")
    elif pxtype == "funding":
        return "rate"
    else:
        return "px"


async def _get_filtered_catalog_items(
    asset_type: CatalogAssetType,
    exchange_pxtype_pairs: set[tuple[str, PxType]],
    start: str,
    end: str,
    base_assets: set[str],
    quote_assets: set[str] | None = None,
    tenors: list[TenorItem] | None = None,
) -> set[CatalogItemDataclass]:
    tenors = tenors or []

    if asset_type == "spot":
        # NOTE: excluding blockscholes for spot only necessary due to DEX prices
        # stored not being driven by ssm param (but by histgen instead). Once
        # prices are stored from blockstream this can be removed
        instrument_exchanges = set(
            [pair[0] for pair in exchange_pxtype_pairs if "blockscholes" != pair[0]]
        )
    else:
        instrument_exchanges = set([pair[0] for pair in exchange_pxtype_pairs])

    # TODO: requesting both blocksholes/v2composite + individual exchanges
    # seems broken so have to split in two. Investigate
    blockscholes_items = []
    other_items = []
    if (
        "blockscholes" in instrument_exchanges
        or "blockscholes-syn" in instrument_exchanges
    ):
        blockscholes_items = await get_instruments_async(
            fields=[],
            start=start,
            end=end,
            exchanges=["blockscholes"],
            asset_types=[asset_type],
            base_assets=list(base_assets),
            quote_assets=list(quote_assets or []),
        )
        if not blockscholes_items and asset_type in ["perpetual", "spot"]:
            # no instruments found if no index is produced for the given base
            # asset, but series may still exist as synthetic backfill, so stubbing
            # out instrument to enable request for data downstream
            logging.warning(
                f"Datagrabber returned no instruments for {base_assets}, likely due to assets not being present in index config. Stubbing out to enable timeseries request."
            )
            blockscholes_items = [
                {
                    "qualified_name": f"blockscholes.{asset_type}.contracts",
                    "base_asset": b,
                    "quote_asset": "USD",
                    "instrument": f"{b}_USD",
                }
                for b in base_assets
            ]

        instrument_exchanges.discard("blockscholes")
        instrument_exchanges.discard("blockscholes-syn")

    if instrument_exchanges:
        other_items = await get_instruments_async(
            fields=[],
            start=start,
            end=end,
            exchanges=list(instrument_exchanges),
            asset_types=[asset_type],
            base_assets=list(base_assets),
            quote_assets=list(quote_assets or []),
        )

    catalog_items = [*blockscholes_items, *other_items]
    filtered_catalog_items: set[CatalogItemDataclass] = set()

    cond_to_remove = cast(TenorCondition, {})
    if tenors:
        for t in tenors:
            for row in catalog_items:
                catalog_item = CatalogItemDataclass.from_dict(row)

                if isinstance(t, dict):
                    cond_to_remove = t
                    if _is_catalog_item_expiry_in_bounds(
                        tenor_cond=t,
                        catalog_item=catalog_item,
                        start=start,
                        end=end,
                    ):
                        filtered_catalog_items.add(catalog_item)
                elif utils_general.normalise_expiry(catalog_item.expiry) == t:
                    filtered_catalog_items.add(catalog_item)

        # TODO: long-range side effect since downstream logic assumes only
        # genuine tenors present and that they are filtered based on condition.
        # Refactor & move somewhere else more obvious
        if cond_to_remove:
            tenors.remove(cond_to_remove)
        for item in filtered_catalog_items:
            tenors.append(utils_general.normalise_expiry(item.expiry))
    else:
        filtered_catalog_items = {
            CatalogItemDataclass.from_dict(row) for row in catalog_items
        }

    return filtered_catalog_items


def _process_exchange_for_catalog(
    exhange_pxtype_pairs_chunk: list[tuple[str, PxType]],
    catalog_items: list[CatalogItemDataclass],
    asset_type_param: str,
    versions: Sequence[str],
    freq: str,
) -> dict[str, CatalogItemDict]:
    qn_to_catalog_item: dict[str, CatalogItemDict] = {}
    for exchange, pxtype in exhange_pxtype_pairs_chunk:
        for item in catalog_items:
            if item.qualified_name.split(".")[0] != exchange:
                continue

            catalog_item = item.to_dict()
            suf = _pxtype_suffix(pxtype, asset_type_param)
            if suf.endswith("pxs"):
                target_instr = item.instrument
                if exchange != "blockscholes":
                    # T prices are always stored on BS format, so exchange
                    # specific instruments require conversion
                    assert (
                        "expiry" in catalog_item
                        and "strike" in catalog_item
                        and "type" in catalog_item
                    )
                    target_instr = utils_general.generate_bs_instrument_name(
                        AssetTypes(asset_type_param),
                        catalog_item["base_asset"],
                        "USD",
                        catalog_item["expiry"],
                        catalog_item["strike"],
                        (
                            OptionTypes.CALL
                            if catalog_item["type"] == "C"
                            else OptionTypes.PUT
                        ),
                    )
                qn_instr = "_".join(target_instr.split("_")[:3])

                # these have to be added later since prices for all instruments
                # on a given expiry are bundled on theoretical px option items
                catalog_item.pop("strike", None)
                catalog_item.pop("type", None)
                catalog_item["instrument"] = qn_instr

                for v in versions:
                    versioned_exch = f"{v + '.' if v else v}{exchange}"
                    qn = f"{versioned_exch}.{asset_type_param}.{qn_instr}.{freq}.{pxtype}.{suf}"
                    qn_to_catalog_item[qn] = catalog_item

            else:
                full_suf = f"{pxtype}.{suf}" if pxtype != "trade" else "px"

                qn = (
                    f"{exchange}.{asset_type_param}.{item.instrument}.{freq}.{full_suf}"
                )
                if asset_type_param == "option":
                    # strike field not correctly & consistently set in catalog,
                    # parsing as workaround for now (eg. XRP_USDC-28MAR24-0d55-P)
                    # bybit USDT options format: BTC-18MAR25-75000-C-USDT
                    strike_idx = (
                        -3
                        if (exchange == "bybit" and item.quote_asset == "USDT")
                        else -2
                    )
                    strike_tokens = item.instrument.split("-")[strike_idx].split("d")
                    catalog_item["strike"] = (
                        float(f"{strike_tokens[0]}.{strike_tokens[1]}")
                        if len(strike_tokens) > 1
                        else int(strike_tokens[0])
                    )
                qn_to_catalog_item[qn] = catalog_item
    return qn_to_catalog_item


def _process_exchange_for_catalog_and_send_results(
    asset_type_param: str,
    versions: Sequence[str],
    freq: str,
    catalog_items: list[CatalogItemDataclass],
    chunk: list[tuple[str, PxType]],
    conn: Connection,
) -> None:
    try:
        results = _process_exchange_for_catalog(
            exhange_pxtype_pairs_chunk=chunk,
            catalog_items=catalog_items,
            asset_type_param=asset_type_param,
            versions=versions,
            freq=freq,
        )
        conn.send(results)
    except Exception as e:
        conn.send({"error": str(e), "details": ""})
        logging.exception("Error processing data chunk")
    conn.close()


async def _construct_qns_details_px(
    start: str,
    end: str,
    exchanges: ExchangePxTypeMap,
    versions: Sequence[str],
    base_assets: Sequence[str],
    freq: str,
    asset_type_param: CatalogAssetType,
    quote_assets: Sequence[str] | None = None,
    tenors: list[TenorItem] | None = None,
) -> dict[str, CatalogItemDict]:
    exchange_pxtype_pairs = _get_exchange_pxtype_pairs(exchanges)
    catalog_items = await _get_filtered_catalog_items(
        asset_type=asset_type_param,
        exchange_pxtype_pairs=exchange_pxtype_pairs,
        start=start,
        end=end,
        base_assets=set(base_assets),
        quote_assets=set(quote_assets or []),
        tenors=tenors,
    )

    qn_to_catalog_item: dict[str, CatalogItemDict] = {}
    processed_pairs: set[tuple[str, PxType]] = set()
    for e, pxtype in exchange_pxtype_pairs:
        if (
            asset_type_param in ["spot", "perpetual"]
            and e in ["blockscholes", "bybit", "deribit"]
            and pxtype == "index"
        ):
            for c in base_assets:
                instr = ""
                if e == "blockscholes":
                    instr = f"{c}_USD"
                if e in ["deribit", "bybit"]:
                    instr = f"{c}USD"
                qn = f"{e}.{asset_type_param}.{instr}.{freq}.index.px"
                qn_to_catalog_item[qn] = {
                    "base_asset": c,
                    "quote_asset": "USD",
                    "exchange": e,
                    "asset_type": asset_type_param,
                    "instrument": instr,
                }
            processed_pairs.add((e, pxtype))
    exchange_pxtype_pairs.difference_update(processed_pairs)

    if not exchange_pxtype_pairs:
        return qn_to_catalog_item

    results = cast(
        list[dict[str, CatalogItemDict]],
        parallel_process(
            data_slices=list(exchange_pxtype_pairs),
            num_workers=get_num_required_workers(exchange_pxtype_pairs),
            process_chunk_fn=functools.partial(
                _process_exchange_for_catalog_and_send_results,
                asset_type_param,
                versions,
                freq,
                list(catalog_items),
            ),
            chunk_data=True,
        ),
    )
    for d in results:
        qn_to_catalog_item.update(d)

    return qn_to_catalog_item


def _should_skip(params: ParamsPx, catalog_item: CatalogItemDict) -> bool:
    asset_params: AssetParams = params["asset_params"]
    assert "asset_type" in catalog_item

    if catalog_item["asset_type"] == "option":
        assert "strike" in catalog_item
        asset_params = cast(AssetOptionParams, asset_params)
        strikes = asset_params["strikes"]
        strike_tokens = f"{float(catalog_item['strike']):.9f}".rstrip("0").split(".")
        strike_str = (
            ".".join(strike_tokens)
            if (len(strike_tokens) > 1 and strike_tokens[1])
            else strike_tokens[0]
        )
        item_it = catalog_item.get("type")
        instrument_types = asset_params["instrument_types"]

        not_in_requested_strikes = strikes and strike_str not in strikes
        not_in_requested_types = instrument_types and item_it not in instrument_types

        if not_in_requested_strikes or not_in_requested_types:
            return True

    return False


def _process_raw_px_chunk(
    params: ParamsPx,
    qn_to_catalog_details: dict[str, CatalogItemDict],
    dynamo_items_chunk: list[dict[str, Any]],
) -> list[Snap]:
    asset_params: AssetParams = params["asset_params"]
    results: list[Snap] = []
    for item in dynamo_items_chunk:
        item = cast(PxItem, item)
        qn = item["qualified_name"]
        qn_tokens: QnTokens = parse_qualified_name(qn)
        catalog_item = cast(
            CatalogItemDict, copy.deepcopy(qn_to_catalog_details.get(qn, {}))
        )
        if not catalog_item:
            # TODO: is this path still required or can we assert?
            # what catalog entries missing?
            logging.warning(f"Catalog item not found for {qn}")

        pxtype = qn_tokens["pxtype"]
        asset_type = qn_tokens["asset_type"]
        field_name: Literal["annualised_rate", "px", "rate"] = asset_params.get(
            "field", "px" if pxtype != "funding" else "rate"
        )

        if asset_type in ["option", "future"]:
            assert "expiry" in catalog_item

            ymd_instr_expiry = True if qn_tokens["exchange"] == "okx" else False
            exp = to_iso(
                utils_general.normalise_expiry(
                    catalog_item["expiry"],
                    ymd_format=ymd_instr_expiry,
                )
            )
            catalog_item["expiry"] = exp
            catalog_item["asset_type"] = asset_type

            if asset_type == "option" and pxtype == "theoretical":
                asset_params = cast(AssetOptionParams, asset_params)
                strikes_param = asset_params["strikes"]
                # assuming C and P have same set of strikes
                pxs = get_pxs_dict(cast(TOptionPxItem, item))
                strikes = strikes_param if strikes_param else pxs["C"].keys()
                its = (
                    asset_params["instrument_types"]
                    if asset_params["instrument_types"]
                    else pxs.keys()
                )
                pxs_instrument = catalog_item["instrument"]

                for s in strikes:
                    for it in its:
                        strike_tokens = f"{float(s):.9f}".rstrip("0").split(".")
                        item_strike = (
                            "d".join(strike_tokens)
                            if (len(strike_tokens) > 1 and strike_tokens[1])
                            else strike_tokens[0]
                        )
                        catalog_item["instrument"] = (
                            f"{pxs_instrument}_{item_strike}_{it}"
                        )
                        catalog_item["strike"] = float(s)
                        catalog_item["type"] = it
                        results.append(
                            cast(
                                Snap,
                                {
                                    **item,
                                    **catalog_item,
                                    "px": pxs[it][str(float(s))],
                                },
                            )
                        )
                continue

        if _should_skip(params=params, catalog_item=catalog_item):
            continue

        results.append(
            cast(
                Snap,
                {
                    **item,
                    **catalog_item,
                    field_name: get_field_value(
                        pxtype=pxtype,
                        row=item,
                        suf=qn_tokens["suf"],
                        asset_type=asset_type,
                        it=catalog_item.get("type"),
                        strike=catalog_item.get("strike"),
                        field=field_name,
                    ),
                },
            )
        )
    return results


def _process_raw_px_chunk_and_send_results(
    params: ParamsPx,
    qn_to_catalog_details: dict[str, CatalogItemDict],
    chunk: list[dict[str, Any]],
    conn: Connection,
) -> None:
    try:
        results = _process_raw_px_chunk(
            dynamo_items_chunk=chunk,
            params=params,
            qn_to_catalog_details=qn_to_catalog_details,
        )
        conn.send(results)
    except Exception as e:
        conn.send({"error": str(e), "details": ""})
        logging.exception("Error processing data chunk")
    conn.close()


async def retrieve_data(
    config: TargetConfig,
) -> RetrieveData:
    params = config["params"]
    base_assets = params["base_assets"]
    quote_assets = params.get("quote_assets")
    freq = params["freq"]
    versions = params["versions"]
    retrieval_filter = params.get("filter")
    target = config["target"]
    start_ns = utils_general.to_datetime(QUERY_START).timestamp() * 1e9
    end_ns = utils_general.to_datetime(QUERY_END).timestamp() * 1e9

    qn_to_catalog_details = {}
    t = time.time()

    conversion_qfns = None

    if target == "iv":
        assert config["target"] == "iv"
        exchanges = cast(list[str], params["exchanges"])
        models = config["params"]["models"]
        level_type = config["params"]["level"]["type"]
        tenors = config["params"]["tenors"]
        qfns = _construct_qns_iv(
            tenors, exchanges, versions, level_type, models, base_assets, freq
        )

    elif target.startswith("px"):
        assert config["target"] == "px"
        asset_type_param = config["params"]["asset_params"]["asset_type"]
        exchanges = cast(ExchangePxTypeMap, params["exchanges"])
        tenors = config["params"].get("tenors")
        qn_to_catalog_details = await _construct_qns_details_px(
            start=QUERY_START,
            end=QUERY_END,
            exchanges=exchanges,
            versions=versions,
            base_assets=base_assets,
            quote_assets=quote_assets,
            freq=freq,
            asset_type_param=asset_type_param,
            tenors=tenors,
        )
        qfns = list(qn_to_catalog_details.keys())

        # Validate conversion quote
        output_quote_asset = (
            config["params"]
            .get("transformation_rules", {})
            .get("output_quote_asset", "USD")
        )

        if output_quote_asset and freq != "tick":
            conversion_base_assets: set[str] = set()
            for key, details in qn_to_catalog_details.items():
                quote_asset = details.get("quote_asset")
                if quote_asset and quote_asset not in (output_quote_asset, "USD"):
                    conversion_base_assets.add(quote_asset)

            if output_quote_asset != "USD":
                conversion_base_assets.add(output_quote_asset)

            if conversion_base_assets:
                conv_qn_to_catalog_details = await _construct_qns_details_px(
                    start=QUERY_START,
                    end=QUERY_END,
                    exchanges={("blockscholes",): ["index"]},
                    versions=[],
                    base_assets=list(conversion_base_assets),
                    quote_assets=["USD"],  # We only store index.px in USD
                    freq=freq,
                    asset_type_param="spot",
                    tenors=None,
                )

                assert len(conversion_base_assets) == len(
                    conv_qn_to_catalog_details
                ), "failed to find all conversion instruments"

                qn_to_catalog_details.update(conv_qn_to_catalog_details)
                conversion_qfns = conv_qn_to_catalog_details.keys()
                qfns.extend(conversion_qfns)
    else:
        raise ValueError(f"Invalid target: {target}")
    logging.info(f"Qns + catalog details construction took {round(time.time() - t)}s")

    t = time.time()
    queries: list[GrabParams] = []
    # entity_type = "timeseries_old"
    entity_type = "timeseries"
    if retrieval_filter:
        for t in _generate_filter_timestamps(QUERY_START, QUERY_END, retrieval_filter):
            queries.extend(
                construct_timeseries_queries(qfns, t, t, [], entity_type=entity_type)
            )
    else:
        queries = construct_timeseries_queries(
            qfns, start_ns, end_ns, [], entity_type=entity_type
        )
    dynamo_items = cast(list[dict[str, Any]], await grab_async(queries))

    logging.info(f"Grabbing {len(dynamo_items)} items took {round(time.time() - t)}s")
    if not dynamo_items:
        return RetrieveData(conversion_data=[], raw_data=[])
    t = time.time()

    conversion_data: list[Snap] = []
    raw_data: list[Snap] = []
    if target == "iv":
        assert not qn_to_catalog_details
        raw_data = sorted(dynamo_items, key=lambda dp: dp["timestamp"])

    elif target.startswith("px"):
        assert qn_to_catalog_details
        px_params = cast(ParamsPx, config["params"])

        results = cast(
            list[Snap],
            parallel_process(
                data_slices=dynamo_items,
                num_workers=get_num_required_workers(dynamo_items),
                process_chunk_fn=functools.partial(
                    _process_raw_px_chunk_and_send_results,
                    px_params,
                    qn_to_catalog_details,
                ),
                chunk_data=True,
            ),
        )
        results = sorted(results, key=lambda dp: dp["timestamp"])

        if conversion_qfns:
            for r in results:
                if r["qualified_name"] in conversion_qfns:
                    conversion_data.append(r)
                else:
                    raw_data.append(r)
        else:
            raw_data = results

    else:
        raise ValueError(f"Invalid target: {target}")

    logging.info(
        f"Processing raw data into {len(raw_data)} results took {round(time.time() - t)}s"
    )
    return RetrieveData(
        conversion_data=conversion_data,
        raw_data=raw_data,
    )
