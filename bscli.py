#!/usr/bin/env python3.8

import asyncio
import sys

if __name__ == "__main__":
    if sys.argv[1] == "create_ecs_stream":
        import bscli.create_ecs_stream.create_ecs_stream as create_ecs_stream

        params = create_ecs_stream.generate_params(
            pascal_name=sys.argv[2],
            repo_name=sys.argv[3],
            stage=sys.argv[4],
        )

        create_ecs_stream.run(params=params)

    elif sys.argv[1] == "create_prod_release":
        sys.argv = [sys.argv[0]] + sys.argv[2:]
        import create_prod_release.create_prod_release as create_prod_release

        asyncio.get_event_loop().run_until_complete(create_prod_release.run())

    elif sys.argv[1] == "connect_ec2":
        sys.argv = [sys.argv[0]] + sys.argv[2:]
        import connect_ec2.connect_ec2 as connect_ec2

        connect_ec2.run()

    elif sys.argv[1] == "data_qa":
        sys.argv = [sys.argv[0]] + sys.argv[2:]
        import data_qa.data_qa as data_qa

        asyncio.run(data_qa.run())

    elif sys.argv[1] == "fetch_client_subs":
        sys.argv = [sys.argv[0]] + sys.argv[2:]
        import fetch_client_subs.fetch_client_subs as fetch_client_subs

        asyncio.run(fetch_client_subs.main())

    else:
        print("Invalid command.")
        sys.exit(1)
