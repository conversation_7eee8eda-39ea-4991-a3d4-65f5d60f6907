AWSTemplateFormatVersion: 2010-09-09
Resources:
  {{camelcase_name}}ContainerRole:
    Type: 'AWS::IAM::Role'
    Properties:
      Path: /
      RoleName: {{camelcase_name}}ContainerRole
      AssumeRolePolicyDocument: >-
        {"Version":"2012-10-17","Statement":[{"Sid":"","Effect":"Allow","Principal":{"Service":"ecs-tasks.amazonaws.com"},"Action":"sts:AssumeRole"}]}
      MaxSessionDuration: 3600
      ManagedPolicyArns:
        - 'arn:aws:iam::aws:policy/AmazonSSMFullAccess'
        - !Sub 'arn:aws:iam::${AWS::AccountId}:policy/blockstreamAgent{{pascalcase_stage}}Policy'
        - !Ref {{camelcase_name}}KinesisPolicy
      Description: Allows {{camelcase_name}} ECS tasks to access necessary AWS resources.
    DependsOn:
      - {{camelcase_name}}KinesisPolicy

  {{camelcase_name}}DeployPolicy:
    Type: 'AWS::IAM::ManagedPolicy'
    Properties:
      ManagedPolicyName: {{camelcase_name}}DeployBotPolicy
      Path: /
      PolicyDocument: !Sub |
        {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Sid": "VisualEditor0",
                    "Effect": "Allow",
                    "Action": [
                        "ecs:UpdateService",
                        "ecs:RegisterTaskDefinition",
                        "ecr:GetAuthorizationToken",
                        "ecs:DescribeServices",
                        "ecs:TagResource"
                    ],
                    "Resource": "*"
                },
                {
                    "Sid": "VisualEditor1",
                    "Effect": "Allow",
                    "Action": [
                        "iam:PassRole",
                        "ecr:GetDownloadUrlForLayer",
                        "ecr:BatchGetImage",
                        "ecr:CompleteLayerUpload",
                        "ecr:UploadLayerPart",
                        "ecr:InitiateLayerUpload",
                        "ecr:BatchCheckLayerAvailability",
                        "ecr:PutImage"
                    ],
                    "Resource": [
                        "arn:aws:iam::${AWS::AccountId}:role/${{{camelcase_name}}ContainerRole}",
                        "arn:aws:ecr:${AWS::Region}:${AWS::AccountId}:repository/${{{camelcase_name}}Repository}"
                    ]
                }
            ]
        }
      Users:
        - ContainerDeployBot
    DependsOn:
      - {{camelcase_name}}Repository
      - {{camelcase_name}}ContainerRole

  {{camelcase_name}}Repository:
    Type: 'AWS::ECR::Repository'
    Properties:
      RepositoryName: {{lowercase_name}}-{{lowercase_stage}}
      LifecyclePolicy:
        LifecyclePolicyText: >-
          {"rules":[{"rulePriority":1,"description":"Retire oldest
          images","selection":{"tagStatus":"untagged","countType":"imageCountMoreThan","countNumber":3},"action":{"type":"expire"}}]}
        RegistryId: !Ref 'AWS::AccountId'
      Tags:
        - Key: {{pascalcase_name}}{{pascalcase_stage}}
          Value: '1'

  {{camelcase_name}}LogGroup:
    Type: 'AWS::Logs::LogGroup'
    Properties:
      LogGroupName: /ecs/{{camelcase_name}}-containers-{{lowercase_stage}}
      RetentionInDays: 7
      Tags:
        - Key: {{pascalcase_name}}{{pascalcase_stage}}
          Value: '1'

  {{camelcase_name}}ErrorMetricFilter:
    Type: AWS::Logs::MetricFilter
    Properties:
      LogGroupName:
        Ref: {{camelcase_name}}LogGroup
      FilterName: "{{pascalcase_name}}_Errors"
      FilterPattern: "ERROR"
      MetricTransformations:
        -
          MetricValue: "1"
          MetricNamespace: "Streaming"
          MetricName: "{{pascalcase_name}}_Errors"
          Unit: "Count"

  {{camelcase_name}}SecurityGroupRule:
    Type: 'AWS::EC2::SecurityGroupIngress'
    Properties:
      FromPort: 0
      IpProtocol: tcp
      ToPort: 65535
      SourceSecurityGroupId: !Ref {{camelcase_name}}SecurityGroup
      GroupId: !Ref {{camelcase_name}}SecurityGroup
    DependsOn:
      - {{camelcase_name}}SecurityGroup

  {{camelcase_name}}SecurityGroup:
    Type: 'AWS::EC2::SecurityGroup'
    Properties:
      GroupDescription: Security group for {{pascalcase_name}} service
      GroupName: {{camelcase_name}}SG
      Tags:
        - Key: {{pascalcase_name}}{{pascalcase_stage}}
          Value: '1'
      VpcId:
        Ref: VpcIdParameter
      SecurityGroupEgress:
        - CidrIp: 0.0.0.0/0
          IpProtocol: '-1'

  {{camelcase_name}}KinesisPolicy:
    Type: 'AWS::IAM::ManagedPolicy'
    Properties:
      ManagedPolicyName: {{camelcase_name}}{{pascalcase_stage}}KinesisPolicy
      Path: /
      PolicyDocument: !Sub |
        {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Sid": "VisualEditor0",
                    "Effect": "Allow",
                    "Action": [
                        "kinesis:DeregisterStreamConsumer",
                        "kinesis:SubscribeToShard",
                        "kinesis:DecreaseStreamRetentionPeriod",
                        "kinesis:PutRecords",
                        "kinesis:DescribeStreamConsumer",
                        "kinesis:CreateStream",
                        "kinesis:GetShardIterator",
                        "kinesis:DescribeStream",
                        "kinesis:RegisterStreamConsumer",
                        "kinesis:ListTagsForStream",
                        "kinesis:PutRecord",
                        "kinesis:RemoveTagsFromStream",
                        "kinesis:DeleteStream",
                        "kinesis:DescribeStreamSummary",
                        "kinesis:SplitShard",
                        "kinesis:MergeShards",
                        "kinesis:AddTagsToStream",
                        "kinesis:IncreaseStreamRetentionPeriod",
                        "kinesis:GetRecords",
                        "kinesis:ListStreamConsumers",
                        "kinesis:UpdateStreamMode",
                        "kinesis:ListStreams",
                        "kinesis:EnableEnhancedMonitoring",
                        "kinesis:ListShards",
                        "kinesis:UpdateShardCount",
                        "kinesis:DescribeLimits",
                        "kinesis:DisableEnhancedMonitoring"
                    ],
                    "Resource": [
                        "arn:aws:kinesis:*:${AWS::AccountId}:*/*/consumer/*:*",
                        "arn:aws:kinesis:${AWS::Region}:${AWS::AccountId}:stream/channel_*"
                    ]
                }
            ]
        }

  {{camelcase_name}}Service:
    Type: 'AWS::ECS::Service'
    Properties:
      ServiceName: {{camelcase_name}}-service
      Cluster: {{cluster_name}}
      DesiredCount: !Ref {{pascalcase_name}}Count
      LaunchType: FARGATE
      PlatformVersion: LATEST
      TaskDefinition: !Sub >-
        arn:aws:ecs:${AWS::Region}:${AWS::AccountId}:task-definition/{{camelcase_name}}-task-definition:${{{pascalcase_name}}TaskDefVersion}
      DeploymentConfiguration:
        MaximumPercent: 200
        MinimumHealthyPercent: 100
        DeploymentCircuitBreaker:
          Enable: false
          Rollback: false
      NetworkConfiguration:
        AwsvpcConfiguration:
          AssignPublicIp: DISABLED
          SecurityGroups:
            - !Ref {{camelcase_name}}SecurityGroup
          Subnets:
            - Ref: PrivateSubnetOneIdParameter
            - Ref: PrivateSubnetTwoIdParameter
      SchedulingStrategy: REPLICA
    DependsOn:
      - {{camelcase_name}}TaskDefinition

  {{camelcase_name}}TaskDefinition:
    Type: 'AWS::ECS::TaskDefinition'
    Properties:
      ContainerDefinitions:
        - Essential: true
          Image: !Sub >-
            ${AWS::AccountId}.dkr.ecr.${AWS::Region}.amazonaws.com/{{lowercase_name}}-{{lowercase_stage}}:latest
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Sub '${{{camelcase_name}}LogGroup}'
              awslogs-region: !Ref 'AWS::Region'
              awslogs-stream-prefix: ecs
          Name: {{camelcase_name}}-container
          PortMappings:
            - ContainerPort: 8080
              HostPort: 8080
              Protocol: tcp
      Family: {{camelcase_name}}-task-definition
      TaskRoleArn: !GetAtt {{camelcase_name}}ContainerRole.Arn
      ExecutionRoleArn: !Sub 'arn:aws:iam::${AWS::AccountId}:role/ecsTaskExecutionRole'
      NetworkMode: awsvpc
      RequiresCompatibilities:
        - FARGATE
      Cpu: '2048'
      Memory: '4096'
    DependsOn:
      - {{camelcase_name}}Repository
      - {{camelcase_name}}ContainerRole
      - {{camelcase_name}}LogGroup
      - {{camelcase_name}}SecurityGroup

Parameters:
  VpcIdParameter:
    Type: String
    Default: {{vpc_id}}
    Description: Enter VPC ID
  PrivateSubnetOneIdParameter:
    Type: String
    Default: {{private_one_subnet}}
    Description: Enter ID Of Private Subnet 1
  PrivateSubnetTwoIdParameter:
    Type: String
    Default: {{private_two_subnet}}
    Description: Enter ID Of Private Subnet 2
  {{pascalcase_name}}Count:
    Type: Number
    Default: 0
    Description: Number of desired containers
  {{pascalcase_name}}TaskDefVersion:
    Type: Number
    Default: 1
    Description: Desired task definition version for containers
