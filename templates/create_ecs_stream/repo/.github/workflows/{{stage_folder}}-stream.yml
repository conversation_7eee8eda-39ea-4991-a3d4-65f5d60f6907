name: {{pascalcase_stage}} Stream Deployment

on:
  workflow_dispatch:

env:
  AWS_REGION: eu-west-2
  ECR_REPOSITORY: {{lowercase_name}}-{{lowercase_stage}}
  ECS_SERVICE: {{camelcase_name}}-service
  ECS_CLUSTER: {{cluster_name}}
  ECS_TASK_DEFINITION: stream/task-definition-{{stage_folder}}.json
  CONTAINER_NAME: {{camelcase_name}}-container

permissions:
  contents: read

jobs:
  deploy:
    if: github.repository == 'blockscholes/{{repo_name}}'
    name: Deploy
    runs-on: ubuntu-24.04
    environment: {{lowercase_stage}}

    steps:
    - name: Get current date
      id: date
      run: echo "date=$(date +'%Y-%m-%d_%H.%M')" >> $GITHUB_OUTPUT
      
    - name: Checkout
      uses: actions/checkout@v3

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_{{upper_short_stage}} }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_{{upper_short_stage}} }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build, tag, and push image to Amazon ECR
      id: build-image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: ${{ github.sha }}
        DATE: ${{ steps.date.outputs.date }}
      run: |
        # Build a docker container and
        # push it to ECR so that it can
        # be deployed to ECS.
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG -t $ECR_REGISTRY/$ECR_REPOSITORY:$DATE -t $ECR_REGISTRY/$ECR_REPOSITORY:latest -f stream/Dockerfile .
        docker push --all-tags $ECR_REGISTRY/$ECR_REPOSITORY
        echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT
    - name: Fill in the new image ID in the Amazon ECS task definition
      id: task-def
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: ${{ env.ECS_TASK_DEFINITION }}
        container-name: ${{ env.CONTAINER_NAME }}
        image: ${{ steps.build-image.outputs.image }}

    - name: Deploy Amazon ECS task definition
      uses: aws-actions/amazon-ecs-deploy-task-definition@v1
      with:
        task-definition: ${{ steps.task-def.outputs.task-definition }}
        service: ${{ env.ECS_SERVICE }}
        cluster: ${{ env.ECS_CLUSTER }}
        wait-for-service-stability: false
