import asyncio
import logging
import os

import utils_general
from block_stream import Agent

utils_general.setup_python_logger(os.getenv("LOG_LEVEL", "INFO"))

agent = Agent("{{pascalcase_name}}")
tick_data_stream = agent.channel(
    "tickData", auto_flush_s=None, seconds_per_checkpoint=None
)


async def live_subscriber():
    async for tick in tick_data_stream:
        print(tick)


if __name__ == "__main__":
    try:
        asyncio.run(live_subscriber())

    except Exception as e:
        logging.exception(f"Run failed: {e}")
