{"containerDefinitions": [{"name": "{{camelcase_name}}-container", "image": "public.ecr.aws/docker/library/python:3.8", "cpu": 0, "portMappings": [{"containerPort": 8766, "hostPort": 8766, "protocol": "tcp"}, {"containerPort": 80, "hostPort": 80, "protocol": "tcp"}], "essential": true, "environment": [{"name": "stage", "value": "{{lowercase_stage}}"}], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/{{camelcase_name}}-containers-{{lowercase_stage}}", "awslogs-region": "eu-west-2", "awslogs-stream-prefix": "ecs", "awslogs-datetime-format": "%Y-%m-%d %H:%M:%S"}}}], "family": "{{camelcase_name}}-task-definition", "taskRoleArn": "arn:aws:iam::{{aws_org_id}}:role/{{camelcase_name}}ContainerRole", "executionRoleArn": "arn:aws:iam::{{aws_org_id}}:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048"}